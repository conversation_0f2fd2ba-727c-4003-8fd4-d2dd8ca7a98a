{"apps": [{"name": "visionrun", "script": "/bin/bash", "args": ["-c", "CUDA_VISIBLE_DEVICES=1 swift deploy --model_type qwen2-vl-7b-instruct-awq --infer_backend vllm --model_id_or_path /home/<USER>/ai_project/opshub-ai-master/llms/models/visionmodel/data --log_interval 0 --port 8001 --host '0.0.0.0' --limit_mm_per_prompt '{\"image\": 2}'"], "env": {"CUDA_VISIBLE_DEVICES": "1"}, "cwd": "/home/<USER>/ai_project/opshub-ai-master", "error_file": "/home/<USER>/ai_project/opshub-ai/llms/models/visionmodel/textrun-vl-error.log", "out_file": "/home/<USER>/ai_project/opshub-ai/llms/models/visionmodel/textrun-vl-out.log", "merge_logs": true}]}