# 定时每天7点和下午四点执行一次,对前后两天好氧池的7点和下午4点左右的数据进行对比
import sys
import os
# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
import logging
import datetime
from config_file import config
from server.utils.pg_tool import query_morning_data

from typing import Dict, List
from config_file import config
from server.utils.get_cameras_info import get_cameras_config
from llms.vllm_api_server_multiple import process_image_multiple
from server.utils.data_publisher import DataPublisher
from server.utils.logger import setup_logging

# 配置日志
setup_logging()
logger = logging.getLogger(__name__)

project_root_config = config.env['environment']['base_paths']['project_root']
image_comparison_config = os.path.join(project_root_config, 'datasets','image_comparison')
if not os.path.exists(image_comparison_config):
    os.makedirs(image_comparison_config)



def determine_time_period():
    """根据当前时间决定是查询上午数据还是下午数据
    
    Returns:
        str: 'morning' 或 'afternoon'
    """
    current_hour = datetime.datetime.now().hour
    
    # 如果当前时间是中午12点之前，查询上午数据；否则查询下午数据
    if current_hour < 12:
        return "morning"
    else:
        return "afternoon"

def compare_images(camera_id, time_period):
    """对比今天和昨天同一时段的图像数据
    
    Args:
        camera_id (str): 摄像头ID
        time_period (str): 时间段，'morning' 或 'afternoon'
    
    Returns:
        dict: 图像对比分析结果
    """
    # 查询指定时间段的数据
    data_result = query_morning_data(camera_id, time_period)
    
    if len(data_result) < 2:
        logger.warning(f"摄像头 {camera_id} 在 {time_period} 时段没有足够的数据进行对比（需要至少2天的数据）")
        return {}
    
    # 按日期排序
    sorted_dates = sorted(data_result.keys())
    
    # 获取最近两天的数据
    yesterday_data = data_result.get(sorted_dates[-2])
    today_data = data_result.get(sorted_dates[-1])
    
    if not yesterday_data or not today_data:
        logger.warning(f"摄像头 {camera_id} 在 {time_period} 时段缺少昨天或今天的数据")
        return {}
    
    # 获取图像路径
    yesterday_image_path = yesterday_data.get('frame_path')
    today_image_path = today_data.get('frame_path')
    
    if not yesterday_image_path or not today_image_path:
        logger.warning(f"摄像头 {camera_id} 在 {time_period} 时段缺少图像路径")
        return {}
    
    # 确保图像文件存在
    if not os.path.exists(yesterday_image_path) or not os.path.exists(today_image_path):
        logger.warning(f"摄像头 {camera_id} 在 {time_period} 时段的图像文件不存在")
        return {}
    
    # 使用process_image_multiple进行图像对比分析
    logger.info(f"对比摄像头 {camera_id} 在 {time_period} 时段的图像: {yesterday_image_path} 和 {today_image_path}")
    result = process_image_multiple(
        frames=[yesterday_image_path, today_image_path],
        system_type='system_prompt_color_comparison'
    )
    
    # 保存分析结果
    result_file = os.path.join(
        image_comparison_config, 
        f"comparison_{camera_id}_{time_period}_{datetime.datetime.now().strftime('%Y%m%d')}.txt"
    )
    with open(result_file, 'w', encoding='utf-8') as f:
        f.write(f"对比摄像头: {camera_id}\n")
        f.write(f"时间段: {time_period}\n")
        f.write(f"昨天图像: {yesterday_image_path}\n")
        f.write(f"今天图像: {today_image_path}\n")
        f.write(f"对比结果: {result}\n")
    
    return result

def main():
    """主函数，执行图像对比任务"""
    cameras_config = get_cameras_config()
    is_api_running = bool(cameras_config)
    
    if not is_api_running:
        logger.error("API未运行，无法获取摄像头配置")
        return
    
    logger.info(f"获取到 {len(cameras_config)} 个摄像头配置")
    
    # 初始化数据发布器
    data_publisher = DataPublisher(config.env)
    
    # 筛选包含aerobic的摄像头配置
    aerobic_cameras = [config for config in cameras_config if 'aerobic' in config['system_type'].lower()]
    
    # 获取这些摄像头的camera_id
    aerobic_camera_ids = [camera['camera_id'] for camera in aerobic_cameras]
    
    logger.info(f"找到 {len(aerobic_camera_ids)} 个好氧池摄像头")
    
    if not aerobic_camera_ids:
        logger.warning("未找到好氧池摄像头，任务结束")
        return
    
    # 确定当前应查询的时间段
    time_period = determine_time_period()
    # time_period = 'morning'
    logger.info(f"当前执行的是 {time_period} 时段的图像对比")
    
    # 对每个好氧池摄像头执行图像对比
    for camera_id in aerobic_camera_ids:
        # 获取摄像头配置信息
        camera_config = next((cam for cam in aerobic_cameras if cam['camera_id'] == camera_id), None)
        if not camera_config:
            logger.warning(f"无法获取摄像头 {camera_id} 的配置信息，跳过")
            continue
            
        video_id = camera_config.get('video_id')
        if not video_id:
            logger.warning(f"摄像头 {camera_id} 缺少video_id，跳过")
            continue
            
        logger.info(f"开始处理摄像头 {camera_id} 的图像对比")
        result = compare_images(camera_id, time_period)
        
        if result:
            # 查询指定时间段的数据获取其他信息
            data_result = query_morning_data(camera_id, time_period)
            
            if not data_result:
                logger.warning(f"摄像头 {camera_id} 在 {time_period} 时段没有数据，跳过")
                continue
                
            # 按日期排序并获取最新数据
            sorted_dates = sorted(data_result.keys())
            today_data = data_result.get(sorted_dates[-1])
            yesterday_data = data_result.get(sorted_dates[-2]) if len(sorted_dates) > 1 else None
            
            if not today_data:
                logger.warning(f"摄像头 {camera_id} 在 {time_period} 时段缺少最新数据，跳过")
                continue
                
            # 处理对比结果，提取需要的字段
            if result.get('是否整体加深或变浅')=='是':
                failure_reasons_type = ['水面整体颜色加深或变浅'] 
                coverage_rate = 99
                coverage_level = 'HIGH'
                alarm_status = 'WARNING'
                analysis_detail = result.get('视觉分析')
                is_abnormal = True
                do_value = 0
                mlss_value = 0
                adjustment_suggestion = result.get('建议')
            else:
                failure_reasons_type = []
                coverage_rate = 1
                coverage_level = 'LOW'
                alarm_status = 'NO_ALARM'
                analysis_detail = '没有发现颜色加深或变浅现象'
                is_abnormal = False
                do_value = 0
                mlss_value = 0
                adjustment_suggestion = '无异常'
                
            # 获取数据库需要的信息
            frame_number = today_data.get('frame_number', 0)
            timestamp = datetime.datetime.now()  # 使用当前时间作为分析时间
            
            # 将前后两天的图像路径用英文逗号拼接
            yesterday_image_path = yesterday_data.get('frame_path', '') if yesterday_data else ''
            today_image_path = today_data.get('frame_path', '')
            combined_frame_path = f"{yesterday_image_path},{today_image_path}"
            
            if len(failure_reasons_type) > 0:
                alarmtype = '运行故障,#f05454'
            else:
                alarmtype = ''
            # 准备发布到数据库的数据
            frame_data = (
                camera_id,                  # camera_id
                video_id,                   # video_id
                frame_number,               # frame_number
                timestamp,                  # timestamp
                combined_frame_path,        # frame_path - 修改为拼接后的路径
                coverage_rate,              # coverage_rate
                coverage_level,             # coverage_level
                alarm_status,               # alarm_status
                analysis_detail,            # analysis_detail
                is_abnormal,                # is_abnormal
                do_value,                   # do_value
                mlss_value,                 # mlss_value
                adjustment_suggestion,      # adjustment_suggestion
                failure_reasons_type,        # failure_reasons_type
                alarmtype,                  # alarmtype
            )
            
            # 发布数据到数据库
            logger.info(f"发布摄像头 {camera_id} 在 {time_period} 时段的分析结果到数据库")
            logger.info(f"使用拼接后的图像路径: {combined_frame_path}")
            data_publisher._publish_to_database(frame_data)
            
        logger.info(f"摄像头 {camera_id} 的图像对比结果: {result}")

if __name__ == "__main__":
    main()
