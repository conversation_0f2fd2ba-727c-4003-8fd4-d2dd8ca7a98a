absl-py==2.1.0
accelerate==1.1.1
addict==2.4.0
aiohappyeyeballs==2.4.3
aiohttp==3.11.7
aiosignal==1.3.1
aliyun-python-sdk-core==2.16.0
aliyun-python-sdk-kms==2.16.5
annotated-types==0.7.0
ansicolors==1.1.8
ansiwrap==0.8.4
anyio==4.6.2.post1
asttokens==2.4.1
async-timeout==5.0.1
attrdict==2.0.1
attrs==24.2.0
autoawq==0.2.7.post2
av==13.1.0
binpacking==1.5.2
certifi==2024.8.30
cffi==1.17.1
chainmap==1.0.3
charset-normalizer==3.4.0
click==8.1.7
cloudpickle==3.1.0
combomethod==1.0.12
compressed-tensors==0.6.0
contourpy==1.3.1
crcmod==1.7
cryptography==43.0.3
cycler==0.12.1
dacite==1.8.1
datasets==3.0.1
decorator==5.1.1
dill==0.3.8
diskcache==5.6.3
distro==1.9.0
docstring_parser==0.16
einops==0.8.0
exceptiongroup==1.2.2
executing==2.1.0
fastapi==0.115.5
filelock==3.13.1
fire==0.7.0
fonttools==4.55.0
frozenlist==1.5.0
fsspec==2024.2.0
future==1.0.0
gguf==0.10.0
grpcio==1.68.0
h11==0.14.0
httpcore==1.0.7
httptools==0.6.4
httpx==0.27.2
huggingface-hub==0.26.2
idna==3.10
importlib_metadata==8.5.0
interegular==0.3.3
intspan==1.6.1
ipython==8.29.0
jedi==0.19.2
jieba==0.42.1
Jinja2==3.1.3
jiter==0.7.1
jmespath==0.10.0
joblib==1.4.2
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
kiwisolver==1.4.7
lark==1.2.2
llvmlite==0.43.0
lm-format-enforcer==0.10.6
lmdeploy==0.6.3
Markdown==3.7
markdown-it-py==3.0.0
MarkupSafe==2.1.5
matplotlib==3.9.2
matplotlib-inline==0.1.7
mdurl==0.1.2
mementos==1.3.1
mistral_common==1.5.1
mmengine-lite==0.10.5
modelscope==1.20.1
mpmath==1.3.0
ms-swift==2.6.0.post2
msgpack==1.1.0
msgspec==0.18.6
multidict==6.1.0
multiprocess==0.70.16
nest-asyncio==1.6.0
networkx==3.2.1
nltk==3.9.1
nulltype==2.3.1
numba==0.60.0
numpy==1.26.3
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.1.105
nvidia-cuda-nvrtc-cu12==12.1.105
nvidia-cuda-runtime-cu12==12.1.105
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==*********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==**********
nvidia-cusparse-cu12==**********
nvidia-ml-py==12.560.30
nvidia-nccl-cu12==2.20.5
nvidia-nvjitlink-cu12==12.1.105
nvidia-nvtx-cu12==12.1.105
openai==1.55.0
opencv-python-headless==*********
options==1.4.10
oss2==2.19.1
outlines==0.0.46
packaging==24.2
pandas==2.2.3
parso==0.8.4
partial-json-parser==*******.post4
peft==0.11.1
pexpect==4.9.0
pillow==10.4.0
platformdirs==4.3.6
prometheus-fastapi-instrumentator==7.0.0
prometheus_client==0.21.0
prompt_toolkit==3.0.48
propcache==0.2.0
protobuf==5.28.3
psutil==6.1.0
ptyprocess==0.7.0
pure_eval==0.2.3
py-cpuinfo==9.0.0
pyairports==2.1.1
pyarrow==18.0.0
pyav==13.1.0
pycountry==24.6.1
pycparser==2.22
pycryptodome==3.21.0
pydantic==2.10.1
pydantic_core==2.27.1
Pygments==2.18.0
pynvml==11.5.3
pyparsing==3.2.0
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
pytz==2024.2
PyYAML==6.0.2
pyzmq==26.2.0
qwen-vl-utils==0.0.8
ray==2.39.0
readline==*******
referencing==0.35.1
regex==2024.11.6
requests==2.32.3
rich==13.9.4
rouge==1.0.1
rpds-py==0.21.0
safetensors==0.4.5
say==1.6.6
scipy==1.14.1
sentencepiece==0.2.0
shortuuid==1.0.13
show==1.6.0
shtab==1.7.1
simplejson==3.19.3
simplere==1.2.13
six==1.12.0
sniffio==1.3.1
sortedcontainers==2.4.0
stack-data==0.6.3
starlette==0.41.3
sympy==1.13.1
tensorboard==2.18.0
tensorboard-data-server==0.7.2
termcolor==2.5.0
textdata==2.4.1
textwrap3==0.9.2
tiktoken==0.7.0
tokenizers==0.20.3
tomli==2.1.0
torch==2.4.0
torchaudio==2.3.1+cu121
torchvision==0.19.0
tqdm==4.67.1
traitlets==5.14.3
transformers==4.46.3
transformers-stream-generator==0.0.5
triton==3.0.0
trl==0.11.4
typeguard==4.4.1
typing_extensions==4.12.2
tyro==0.9.2
tzdata==2024.2
urllib3==2.2.3
uvicorn==0.32.1
uvloop==0.21.0
vllm==0.6.3.post1
watchfiles==1.0.0
wcwidth==0.2.13
websockets==14.1
Werkzeug==3.1.3
xformers==0.0.27.post2
xxhash==3.5.0
yapf==0.43.0
yarl==1.18.0
zipp==3.21.0
zstandard==0.23.0
