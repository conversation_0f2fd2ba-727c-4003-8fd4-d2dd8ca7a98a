import markdown2
from html2docx import html2docx

def convert_with_html2docx(markdown_content, output_file):
    """
    【推荐的回退方案】
    使用 markdown2 + html2docx 将Markdown转换为Word。
    这个方案比手动解析HTML健壮得多。
    
    Args:
        markdown_content (str): Markdown格式的内容字符串
        output_file (str): 输出的Word文档路径
    """
    # 1. 使用 markdown2 转换，它能更好地支持表格、代码块等扩展
    html = markdown2.markdown(markdown_content, extras=["tables", "fenced-code-blocks", "cuddled-lists"])
    
    # 2. 使用html2docx直接将HTML转换为Word文档
    html2docx(html, output_file) 