import logging
import random

from server.task.service import insert_single_test_data, upsert_latest_analysis
from server.utils.mqtt_tool import publish_foam_data
from server.utils.pg_tool import query_data, connect_db
"""
数据发布模块

主要功能：
1. 将分析数据发布到数据库
2. 将覆盖率数据发布到MQTT服务器
3. 支持测试模式和生产模式
"""


class DataPublisher:
    """数据发布器类
    
    负责管理数据的发布，包括数据库存储和MQTT消息发送
    
    Attributes:
        test_mode (bool): 是否处于测试模式，默认为True
            - True: 使用随机数据进行MQTT发布
            - False: 使用实际数据进行MQTT发布
    """

    def __init__(self, config: dict):
        """初始化数据发布器
        
        Args:
            config (dict): 配置字典，包含：
                - system.test_mode: 测试模式开关
        """
        self.test_mode = config.get('system', {}).get('test_mode', True)

    def publish_data(self, frame_data: tuple):
        """发布数据到数据库和MQTT服务器
        
        统一处理数据发布流程，包括数据库存储和MQTT消息发送
        
        Args:
            frame_data (tuple): 帧分析数据元组，包含：
                - camera_id: 摄像头ID
                - video_id: 视频ID
                - frame_number: 帧号
                - timestamp: 时间戳
                - frame_path: 帧图片路径
                - coverage_rate: 覆盖率
                等其他分析数据字段
        """
        try:
            self._publish_to_database(frame_data)
            self._publish_to_mqtt(frame_data)
        except Exception as e:
            logging.error(f"发布数据时发生错误: {str(e)}")

    def _publish_to_database(self, frame_data: tuple):
        """发布数据到数据库
        
        将分析数据保存到数据库的历史表和最新数据表
        
        Args:
            frame_data (tuple): 帧分析数据元组
            
        异常处理：
            记录数据库操作失败的错误信息
        """
        try:
            insert_single_test_data(*frame_data)  # 插入到历史表
            upsert_latest_analysis(*frame_data)  # 更新最新数据表
            logging.info("数据库更新成功")
        except Exception as e:
            logging.error(f"数据库操作失败: {str(e)}")

    def _publish_to_mqtt(self, frame_data: tuple):
        """发布数据到MQTT服务器
        
        根据模式选择发送实际数据或测试数据
        
        Args:
            frame_data (tuple): 帧分析数据元组
            
        特点：
        1. 测试模式下使用随机数据（20-90之间）
        2. 生产模式使用实际覆盖率数据
        3. 发送失败时会记录错误信息
        
        数据格式：
            video_id: 设备ID
            coverage_float: 覆盖率数值
        """
        try:
            video_id = frame_data[1]
            coverage_float = frame_data[5]

            if self.test_mode:
                # 测试模式使用随机数据
                random_number = round(random.uniform(20, 90), 2)
                publish_foam_data(video_id, random_number)
            else:
                # 生产环境使用实际数据
                # print(coverage_float)
                publish_foam_data(video_id, coverage_float)

            logging.info(f"MQTT数据发送成功 - 设备: {video_id}, 覆盖率: {coverage_float}%")
        except Exception as e:
            logging.error(f"MQTT发布失败: {str(e)}")

    def get_coverage_rate(self, camera_id):
        """获取指定摄像头的覆盖率
        
        优先查询历史表中的真实模型识别结果(非1和99的值)，如果没有再查询最新表
        
        Args:
            camera_id: 摄像头ID
            
        Returns:
            float/str: 覆盖率数值或'-'表示无数据
        """
        try:
            # 先从历史表查询最近两天内非1和99的覆盖率数据(模型识别的真实值)
            conn = connect_db()
            cursor = conn.cursor()
            query = """
                SELECT coverage_rate FROM frame_analysis 
                WHERE camera_id = %s 
                AND coverage_rate IS NOT NULL 
                AND coverage_rate != 1 
                AND coverage_rate != 99
                AND timestamp >= NOW() - INTERVAL '2 days'
                ORDER BY timestamp DESC 
                LIMIT 1
            """
            cursor.execute(query, (camera_id,))
            model_result = cursor.fetchone()
            cursor.close()
            conn.close()
            
            # 如果找到了模型识别的真实结果，直接返回
            if model_result and model_result[0] is not None:
                return model_result[0]
                
            # 如果没找到，回退到实时表查询
            result = query_data(
                table="frame_analysis_latest",
                conditions={"camera_id": camera_id},
                fields=[
                    "camera_id", "video_id", "frame_number", "timestamp", "frame_path",
                    "coverage_rate", "coverage_level", "alarm_status", "analysis_detail",
                    "is_abnormal", "do_value", "mlss_value", "adjustment_suggestion",
                    "failure_reasons_type", "failure_reasons_number", "alarmtype"
                ],
                order_by="timestamp DESC",
                limit=1
            )
            if not result:
                return '-'

            # 将查询结果转换为带字段名的字典
            fields = [
                "camera_id", "video_id", "frame_number", "timestamp", "frame_path",
                "coverage_rate", "coverage_level", "alarm_status", "analysis_detail",
                "is_abnormal", "do_value", "mlss_value", "adjustment_suggestion",
                "failure_reasons_type", "failure_reasons_number", "alarmtype"
            ]
            result_dict = dict(zip(fields, result[0]))
            # logging.info("query_by_camera接口请求成功")
            return result_dict.get('coverage_rate')

        except Exception as e:
            logging.error(f"泡沫或者泡沫数据查询失败: {str(e)}")
            return '-'