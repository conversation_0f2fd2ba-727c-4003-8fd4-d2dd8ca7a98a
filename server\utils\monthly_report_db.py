"""/()
设备月报表和场景月报表数据库操作工具

提供对设备月报表(device_monthly_report)和场景月报表(scene_monthly_report)的数据库操作

功能包括：
1. 设备月报：生成、保存、查询、列表、删除单个设备的月报
2. 场景月报：聚合多个设备的月报，生成场景级别的月报
3. 设备场景映射：获取设备与场景的关联关系
"""
import json
import logging
from datetime import datetime, timedelta
import sys
import os
import calendar

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from server.utils import pg_tool
from server.utils.report_templates import generate_monthly_report, generate_scene_analysis
from server.utils.logger import setup_logging
from server.rag.rag_data_tool import create_collection_with_chunks
from server.remote.device import get_device_list, process_device_data, get_scene_devices_list, group_devices_by_scene, get_scene_name_by_id, get_scene_devices_from_api, get_device_scene_mapping

# 配置日志
setup_logging()
logger = logging.getLogger(__name__)
# 为每个设备生成月报
def save_monthly_report(camera_id, report_year=None, report_month=None, submitter="AI巡检员", import_to_knowledge_base=False):
    """
    生成并保存设备月报到数据库
    
    Args:
        camera_id (str): 设备ID/摄像头ID
        report_year (int, optional): 报告年份，默认为当前年份
        report_month (int, optional): 报告月份，默认为当前月份
        submitter (str, optional): 提交人，默认为"AI巡检员"
        import_to_knowledge_base (bool, optional): 是否导入到知识库，默认为True
        
    Returns:
        int: 插入或更新的记录ID，失败返回None
        
    Raises:
        Exception: 数据库操作异常
    """
    # 如果没有提供年份和月份，使用当前年月
    if report_year is None or report_month is None:
        now = datetime.now()
        report_year = report_year or now.year
        report_month = report_month or now.month
    
    try:
        # 生成设备月报
        device_name, normal_count, warning_count, transition_count, normal_analysis, warning_analysis, transition_analysis, full_report_text, summary_analysis, main_failure_types, failure_type_summary, analysis_data = generate_monthly_report(camera_id, report_year, report_month)
        
        # 连接数据库
        conn = pg_tool.connect_db()
        if not conn:
            logger.error("数据库连接失败")
            return None
        
        # 将原始数据转换为JSON字符串（可以根据需要从generate_monthly_report返回）
        raw_data_json = None
        
        try:
            cur = conn.cursor()
            
            # 检查是否已存在该设备当月的报告
            check_sql = """
                SELECT id FROM device_monthly_report
                WHERE camera_id = %s AND report_year = %s AND report_month = %s
            """
            cur.execute(check_sql, (camera_id, report_year, report_month))
            existing_record = cur.fetchone()
            
            if existing_record:
                # 如果已存在，进行更新
                update_sql = """
                    UPDATE device_monthly_report
                    SET device_name = %s,
                        normal_count = %s,
                        warning_count = %s,
                        transition_count = %s,
                        normal_analysis = %s,
                        warning_analysis = %s,
                        transition_analysis = %s,
                        full_report = %s,
                        summary_analysis = %s,
                        raw_data = %s,
                        main_failure_types = %s,
                        failure_type_summary = %s,
                        analysis_data = %s,
                        submitter = %s,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = %s
                    RETURNING id
                """
                cur.execute(update_sql, (
                    device_name, normal_count, warning_count, transition_count,
                    normal_analysis, warning_analysis, transition_analysis,
                    full_report_text, summary_analysis, raw_data_json, 
                    json.dumps(main_failure_types, ensure_ascii=False), failure_type_summary,
                    json.dumps(analysis_data, ensure_ascii=False),
                    submitter, existing_record[0]
                ))
                result = cur.fetchone()
                record_id = result[0] if result else None
                logger.info(f"更新设备 {camera_id} 的月报 ({report_year}年{report_month}月)，ID: {record_id}")
            else:
                # 如果不存在，插入新记录
                insert_sql = """
                    INSERT INTO device_monthly_report (
                        camera_id, device_name, report_year, report_month,
                        normal_count, warning_count, transition_count,
                        normal_analysis, warning_analysis, transition_analysis,
                        full_report, summary_analysis, raw_data, 
                        main_failure_types, failure_type_summary, analysis_data, submitter
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    RETURNING id
                """
                cur.execute(insert_sql, (
                    camera_id, device_name, report_year, report_month,
                    normal_count, warning_count, transition_count,
                    normal_analysis, warning_analysis, transition_analysis,
                    full_report_text, summary_analysis, raw_data_json,
                    json.dumps(main_failure_types, ensure_ascii=False), failure_type_summary, 
                    json.dumps(analysis_data, ensure_ascii=False), submitter
                ))
                result = cur.fetchone()
                record_id = result[0] if result else None
                logger.info(f"插入设备 {camera_id} 的月报 ({report_year}年{report_month}月)，ID: {record_id}")
            
            conn.commit()
            
            # 成功保存月报后，根据参数决定是否导入到知识库
            if record_id and import_to_knowledge_base:
                try:
                    # 生成报告名称用于知识库
                    report_name = f"{report_year}年{report_month}月{device_name}巡检月报"
                    # 组合要导入知识库的文本内容：总结分析 + 完整报告
                    knowledge_base_content = f"""总结分析：
                                            {summary_analysis}
                                            详细报告：
                                            {full_report_text}"""
                    # 使用RAG工具的智能分块函数，确保每个分块都包含报告标题
                    rag_response = create_collection_with_chunks(
                        text_content=knowledge_base_content,
                        collection_name=report_name,
                        title=report_name,  # 传入标题，确保每个分块都包含
                        chunk_size=500,
                        overlap=50,
                        training_type="chunk"
                    )
                    
                    if rag_response and rag_response.status_code == 200:
                        try:
                            result = rag_response.json()
                            chunk_count = result.get('data', {}).get('chunk_count', 1)
                            if chunk_count > 1:
                                logger.info(f"月报已成功分块导入知识库：{report_name}，共{chunk_count}个分块")
                            else:
                                logger.info(f"月报已成功导入知识库：{report_name}")
                        except Exception as parse_error:
                            logger.info(f"月报已成功导入知识库：{report_name}")
                    else:
                        logger.warning(f"月报导入知识库失败：{report_name}, 响应状态: {rag_response.status_code if rag_response else 'None'}")
                        
                except Exception as rag_error:
                    logger.error(f"导入知识库时发生异常: {rag_error}")
                    # 不影响月报保存的成功状态，只记录错误
            
            return record_id
            
        except Exception as e:
            conn.rollback()
            logger.error(f"保存月报数据失败: {e}")
            return None
        finally:
            if cur:
                cur.close()
            if conn:
                conn.close()
                
    except Exception as e:
        logger.error(f"生成或保存月报失败: {e}")
        return None

# 查询每个单独的设备报告
def get_monthly_report(camera_id, report_year=None, report_month=None):
    """
    获取设备月报
    
    Args:
        camera_id (str): 设备ID/摄像头ID
        report_year (int, optional): 报告年份，默认为当前年份
        report_month (int, optional): 报告月份，默认为当前月份
        
    Returns:
        dict: 设备月报数据，失败返回None
        
    Raises:
        Exception: 数据库查询异常
    """
    # 如果没有提供年份和月份，使用当前年月
    if report_year is None or report_month is None:
        now = datetime.now()
        report_year = report_year or now.year
        report_month = report_month or now.month
    
    conn = pg_tool.connect_db()
    if not conn:
        logger.error("数据库连接失败")
        return None
    
    try:
        cur = conn.cursor()
        
        # 查询月报数据，直接从数据库读取report_name字段
        query_sql = """
            SELECT 
                id, camera_id, device_name, report_name, report_year, report_month,
                normal_count, warning_count, transition_count,
                normal_analysis, warning_analysis, transition_analysis,
                full_report, summary_analysis, raw_data, created_at, updated_at, submitter,
                main_failure_types, failure_type_summary, analysis_data
            FROM device_monthly_report
            WHERE camera_id = %s AND report_year = %s AND report_month = %s
        """
        
        cur.execute(query_sql, (camera_id, report_year, report_month))
        result = cur.fetchone()
        
        if not result:
            logger.warning(f"未找到设备 {camera_id} 的月报 ({report_year}年{report_month}月)")
            return None
        
        # 构建返回数据
        fields = [
            "id", "camera_id", "device_name", "report_name", "report_year", "report_month",
            "normal_count", "warning_count", "transition_count",
            "normal_analysis", "warning_analysis", "transition_analysis",
            "full_report", "summary_analysis", "raw_data", "created_at", "updated_at", "submitter",
            "main_failure_types", "failure_type_summary", "analysis_data"
        ]
        
        report_data = dict(zip(fields, result))
        
        # 如果JSON字段是字符串，转换为字典
        json_fields = ['raw_data', 'main_failure_types', 'analysis_data']
        for field in json_fields:
            if isinstance(report_data.get(field), str):
                try:
                    report_data[field] = json.loads(report_data[field])
                except json.JSONDecodeError:
                    logger.error(f"无法解析{field} JSON: {report_data[field]}")
        
        return report_data
        
    except Exception as e:
        logger.error(f"查询月报数据失败: {e}")
        return None
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()


def list_monthly_reports(start_year=None, end_year=None, start_month=None, end_month=None, camera_id=None, limit=100, offset=0):
    """
    列出设备月报列表
    
    Args:
        start_year (int, optional): 开始年份
        end_year (int, optional): 结束年份
        start_month (int, optional): 开始月份
        end_month (int, optional): 结束月份
        camera_id (str, optional): 设备ID过滤
        limit (int): 返回记录数限制，默认100
        offset (int): 偏移量，默认0
        
    Returns:
        list: 月报数据列表
        
    Raises:
        Exception: 数据库查询异常
    """
    conn = pg_tool.connect_db()
    if not conn:
        logger.error("数据库连接失败")
        return []
    
    try:
        cur = conn.cursor()
        
        # 构建查询SQL，直接从数据库读取report_name字段
        query_sql = """
            SELECT 
                id, camera_id, device_name, report_name, report_year, report_month,
                normal_count, warning_count, transition_count,
                created_at, updated_at, submitter
            FROM device_monthly_report
            WHERE 1=1
        """
        
        params = []
        
        # 添加查询条件
        if camera_id:
            query_sql += " AND camera_id = %s"
            params.append(camera_id)
        
        if start_year:
            query_sql += " AND report_year >= %s"
            params.append(start_year)
        
        if end_year:
            query_sql += " AND report_year <= %s"
            params.append(end_year)
            
        if start_month and start_year:
            query_sql += " AND (report_year > %s OR (report_year = %s AND report_month >= %s))"
            params.extend([start_year, start_year, start_month])
            
        if end_month and end_year:
            query_sql += " AND (report_year < %s OR (report_year = %s AND report_month <= %s))"
            params.extend([end_year, end_year, end_month])
        
        # 添加排序、分页
        query_sql += " ORDER BY report_year DESC, report_month DESC, camera_id"
        query_sql += " LIMIT %s OFFSET %s"
        params.extend([limit, offset])
        
        # 执行查询
        cur.execute(query_sql, params)
        results = cur.fetchall()
        
        # 构建返回数据
        fields = [
            "id", "camera_id", "device_name", "report_name", "report_year", "report_month",
            "normal_count", "warning_count", "transition_count",
            "created_at", "updated_at", "submitter"
        ]
        
        reports_list = []
        for row in results:
            report_data = dict(zip(fields, row))
            reports_list.append(report_data)
        
        return reports_list
        
    except Exception as e:
        logger.error(f"列出月报数据失败: {e}")
        return []
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()


def delete_monthly_report(camera_id, report_year, report_month):
    """
    删除设备月报
    
    Args:
        camera_id (str): 设备ID/摄像头ID
        report_year (int): 报告年份
        report_month (int): 报告月份
        
    Returns:
        bool: 删除成功返回True，失败返回False
        
    Raises:
        Exception: 数据库操作异常
    """
    conn = pg_tool.connect_db()
    if not conn:
        logger.error("数据库连接失败")
        return False
    
    try:
        cur = conn.cursor()
        
        # 删除月报记录
        delete_sql = """
            DELETE FROM device_monthly_report
            WHERE camera_id = %s AND report_year = %s AND report_month = %s
            RETURNING id
        """
        
        cur.execute(delete_sql, (camera_id, report_year, report_month))
        result = cur.fetchone()
        
        if result:
            conn.commit()
            logger.info(f"成功删除设备 {camera_id} 的月报 ({report_year}年{report_month}月)，ID: {result[0]}")
            return True
        else:
            logger.warning(f"未找到要删除的月报记录：设备 {camera_id}，{report_year}年{report_month}月")
            return False
            
    except Exception as e:
        conn.rollback()
        logger.error(f"删除月报失败: {e}")
        return False
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()

# 场景报告的入口函数
def save_scene_monthly_report(scene_id, scene_name=None, scene_devices=None, report_year=None, report_month=None, submitter="AI巡检员", import_to_knowledge_base=True):
    # 如果没有提供年份和月份，使用当前年月
    if report_year is None or report_month is None:
        now = datetime.now()
        report_year = report_year or now.year
        report_month = report_month or now.month
    
    # 如果没有提供场景名称，通过API自动获取
    if scene_name is None:
        scene_name = get_scene_name_by_id(scene_id)
        logger.info(f"自动获取场景名称: {scene_id} -> {scene_name}")
    
    try:
        # 1. 获取场景下的设备列表（优先使用传入的设备列表，避免重复调用）
        if scene_devices is None:
            scene_devices = get_scene_devices_from_api(scene_id)
            if not scene_devices:
                logger.warning(f"场景 {scene_id} 下没有找到设备")
                return None
        else:
            logger.info(f"使用传入的设备列表，场景 {scene_id} 包含 {len(scene_devices)} 台设备")
        
        # 2. 从数据库中查询该场景下已有月报的设备
        conn = pg_tool.connect_db()
        if not conn:
            logger.error("数据库连接失败")
            return None
        
        try:
            cur = conn.cursor()
            
            # 构建场景设备ID列表
            scene_camera_ids = [device['camera_id'] for device in scene_devices]
            
            # 查询该场景下已有月报的设备
            query_sql = """
                SELECT camera_id, device_name, normal_count, warning_count, transition_count,
                       normal_analysis, warning_analysis, transition_analysis, 
                       full_report, summary_analysis, main_failure_types, failure_type_summary,
                       analysis_data
                FROM device_monthly_report
                WHERE camera_id = ANY(%s) AND report_year = %s AND report_month = %s
            """
            cur.execute(query_sql, (scene_camera_ids, report_year, report_month))
            results = cur.fetchall()
            
            # 转换为字典列表
            device_reports = []
            total_normal = 0
            total_warning = 0
            total_transition = 0
            
            for row in results:
                device_report = {
                    'camera_id': row[0],
                    'device_name': row[1],
                    'normal_count': row[2],
                    'warning_count': row[3],
                    'transition_count': row[4],
                    'normal_analysis': row[5],
                    'warning_analysis': row[6],
                    'transition_analysis': row[7],
                    'full_report': row[8],
                    'summary_analysis': row[9],
                    'main_failure_types': row[10],
                    'failure_type_summary': row[11],
                    'analysis_data': row[12]
                }
                device_reports.append(device_report)
                total_normal += device_report['normal_count']
                total_warning += device_report['warning_count']
                total_transition += device_report['transition_count']
            
            if not device_reports:
                logger.warning(f"场景 {scene_id} 下没有设备有月报数据")
                return None
            
            # 过滤scene_devices，只保留有月报的设备
            devices_with_reports = [device for device in scene_devices 
                                  if device['camera_id'] in [r['camera_id'] for r in device_reports]]
            
            logger.info(f"场景 {scene_id} 设备统计: 总设备数={len(scene_devices)}, 有月报设备数={len(device_reports)}")
            
            # 3. 生成场景级别的分析
            scene_analysis, full_report, summary_analysis, scene_failure_types, scene_failure_type_summary = generate_scene_analysis(
                devices_with_reports, device_reports, scene_name
            )
            
            # 4. 准备原始数据
            raw_data = {
                "scene_devices": devices_with_reports,  # 只包含有月报的设备
                "device_reports": [{"camera_id": r['camera_id'], "device_name": r['device_name'], 
                                   "normal_count": r['normal_count'], "warning_count": r['warning_count'],
                                   "transition_count": r['transition_count']} for r in device_reports],
                "generation_time": datetime.now().isoformat()
            }
            
            # 5. 保存场景月报到数据库
            insert_sql = """
                INSERT INTO scene_monthly_report (
                    scene_id, scene_name, report_year, report_month,
                    device_count, device_list, 
                    total_normal_count, total_warning_count, total_transition_count,
                    scene_analysis, full_report, summary_analysis, raw_data,
                    main_failure_types, failure_type_summary,
                    created_at, updated_at, submitter
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (scene_id, report_year, report_month)
                DO UPDATE SET
                    scene_name = EXCLUDED.scene_name,
                    device_count = EXCLUDED.device_count,
                    device_list = EXCLUDED.device_list,
                    total_normal_count = EXCLUDED.total_normal_count,
                    total_warning_count = EXCLUDED.total_warning_count,
                    total_transition_count = EXCLUDED.total_transition_count,
                    scene_analysis = EXCLUDED.scene_analysis,
                    full_report = EXCLUDED.full_report,
                    summary_analysis = EXCLUDED.summary_analysis,
                    raw_data = EXCLUDED.raw_data,
                    main_failure_types = EXCLUDED.main_failure_types,
                    failure_type_summary = EXCLUDED.failure_type_summary,
                    updated_at = EXCLUDED.updated_at,
                    submitter = EXCLUDED.submitter
            """
            
            cur.execute(insert_sql, (
                scene_id, scene_name, report_year, report_month,
                len(devices_with_reports), json.dumps(devices_with_reports, ensure_ascii=False),
                total_normal, total_warning, total_transition,
                scene_analysis, full_report, summary_analysis, json.dumps(raw_data, ensure_ascii=False),
                json.dumps(scene_failure_types, ensure_ascii=False), scene_failure_type_summary,
                datetime.now(), datetime.now(), submitter
            ))
            
            conn.commit()
            
            # 6. 导入知识库（如果需要）
            if import_to_knowledge_base:
                try:
                    report_name = f"{report_year}年{report_month}月{scene_name}场景巡检月报"
                    # 使用RAG工具的智能分块函数，确保每个分块都包含报告标题
                    rag_response = create_collection_with_chunks(
                        text_content=full_report,
                        collection_name=report_name,
                        title=report_name,  # 传入标题，确保每个分块都包含
                        chunk_size=500,
                        overlap=50,
                        training_type="chunk"
                    )
                    
                    if rag_response and rag_response.status_code == 200:
                        try:
                            result = rag_response.json()
                            chunk_count = result.get('data', {}).get('chunk_count', 1)
                            if chunk_count > 1:
                                logger.info(f"场景月报已成功分块导入知识库：{report_name}，共{chunk_count}个分块")
                            else:
                                logger.info(f"场景月报已成功导入知识库：{report_name}")
                        except Exception as parse_error:
                            logger.info(f"场景月报已成功导入知识库：{report_name}")
                    else:
                        logger.warning(f"场景月报导入知识库失败：{report_name}, 响应状态: {rag_response.status_code if rag_response else 'None'}")
                        
                    logger.info(f"场景月报已导入知识库: {report_name}")
                except Exception as e:
                    logger.error(f"导入知识库失败: {str(e)}")
            
            logger.info(f"场景月报生成成功: {scene_name} ({report_year}年{report_month}月)")
            return {
                "scene_id": scene_id,
                "scene_name": scene_name,
                "report_year": report_year,
                "report_month": report_month,
                "device_count": len(devices_with_reports),
                "total_devices_in_scene": len(scene_devices),
                "total_normal_count": total_normal,
                "total_warning_count": total_warning,
                "total_transition_count": total_transition
            }
            
        finally:
            cur.close()
            conn.close()
            
    except Exception as e:
        logger.error(f"生成场景月报时发生错误: {str(e)}")
        return None

# 数据库操作函数
def get_scene_monthly_report(scene_id, report_year=None, report_month=None):
    """
    获取场景月报
    
    Args:
        scene_id (str): 场景ID
        report_year (int, optional): 报告年份，默认为当前年份
        report_month (int, optional): 报告月份，默认为当前月份
        
    Returns:
        dict: 场景月报数据，失败返回None
        
    Raises:
        Exception: 数据库查询异常
    """
    # 如果没有提供年份和月份，使用当前年月
    if report_year is None or report_month is None:
        now = datetime.now()
        report_year = report_year or now.year
        report_month = report_month or now.month
    
    conn = pg_tool.connect_db()
    if not conn:
        logger.error("数据库连接失败")
        return None
    
    try:
        cur = conn.cursor()
        
        # 查询场景月报数据，直接从数据库读取report_name字段
        query_sql = """
            SELECT 
                id, scene_id, scene_name, report_year, report_month,
                device_count, device_list, total_normal_count, total_warning_count, total_transition_count,
                scene_analysis, full_report, summary_analysis, raw_data, created_at, updated_at, submitter,
                main_failure_types, failure_type_summary, report_name
            FROM scene_monthly_report
            WHERE scene_id = %s AND report_year = %s AND report_month = %s
        """
        
        cur.execute(query_sql, (scene_id, report_year, report_month))
        result = cur.fetchone()
        
        if not result:
            logger.warning(f"未找到场景 {scene_id} 的月报 ({report_year}年{report_month}月)")
            return None
        
        # 构建返回数据
        fields = [
            "id", "scene_id", "scene_name", "report_year", "report_month",
            "device_count", "device_list", "total_normal_count", "total_warning_count", "total_transition_count",
            "scene_analysis", "full_report", "summary_analysis", "raw_data", "created_at", "updated_at", "submitter",
            "main_failure_types", "failure_type_summary", "report_name"
        ]
        
        report_data = dict(zip(fields, result))
        
        # 如果device_list和raw_data是JSON字符串，转换为字典
        for json_field in ['device_list', 'raw_data', 'main_failure_types']:
            if isinstance(report_data.get(json_field), str):
                try:
                    report_data[json_field] = json.loads(report_data[json_field])
                except json.JSONDecodeError:
                    logger.error(f"无法解析{json_field} JSON: {report_data[json_field]}")
        
        return report_data
        
    except Exception as e:
        logger.error(f"查询场景月报数据失败: {e}")
        return None
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()

# 数据库操作函数
def list_scene_monthly_reports(start_year=None, end_year=None, start_month=None, end_month=None, scene_id=None, limit=100, offset=0):
    """
    列出场景月报列表
    
    Args:
        start_year (int, optional): 开始年份
        end_year (int, optional): 结束年份
        start_month (int, optional): 开始月份
        end_month (int, optional): 结束月份
        scene_id (str, optional): 场景ID过滤
        limit (int): 返回记录数限制，默认100
        offset (int): 偏移量，默认0
        
    Returns:
        list: 场景月报数据列表
        
    Raises:
        Exception: 数据库查询异常
    """
    conn = pg_tool.connect_db()
    if not conn:
        logger.error("数据库连接失败")
        return []
    
    try:
        cur = conn.cursor()
        
        # 构建查询SQL，直接从数据库读取report_name字段
        query_sql = """
            SELECT 
                id, scene_id, scene_name, report_year, report_month,
                device_count, total_normal_count, total_warning_count, total_transition_count,
                main_failure_types, failure_type_summary,
                created_at, updated_at, submitter, report_name
            FROM scene_monthly_report
            WHERE 1=1
        """
        
        params = []
        
        # 添加查询条件
        if scene_id:
            query_sql += " AND scene_id = %s"
            params.append(scene_id)
        
        if start_year:
            query_sql += " AND report_year >= %s"
            params.append(start_year)
        
        if end_year:
            query_sql += " AND report_year <= %s"
            params.append(end_year)
            
        if start_month and start_year:
            query_sql += " AND (report_year > %s OR (report_year = %s AND report_month >= %s))"
            params.extend([start_year, start_year, start_month])
            
        if end_month and end_year:
            query_sql += " AND (report_year < %s OR (report_year = %s AND report_month <= %s))"
            params.extend([end_year, end_year, end_month])
        
        # 添加排序、分页
        query_sql += " ORDER BY report_year DESC, report_month DESC, scene_id"
        query_sql += " LIMIT %s OFFSET %s"
        params.extend([limit, offset])
        
        # 执行查询
        cur.execute(query_sql, params)
        results = cur.fetchall()
        
        # 构建返回数据
        fields = [
            "id", "scene_id", "scene_name", "report_year", "report_month",
            "device_count", "total_normal_count", "total_warning_count", "total_transition_count",
            "main_failure_types", "failure_type_summary",
            "created_at", "updated_at", "submitter", "report_name"
        ]
        
        reports_list = []
        for row in results:
            report_data = dict(zip(fields, row))
            reports_list.append(report_data)
        
        return reports_list
        
    except Exception as e:
        logger.error(f"列出场景月报数据失败: {e}")
        return []
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()


# 测试函数
if __name__ == "__main__":
    # 测试设备月报数据库操作
    camera_id = "4058"
    current_date = datetime.now()
    current_year = current_date.year
    current_month = current_date.month
    
    logger.info(f"测试月报功能，设备ID: {camera_id}, 年月: {current_year}年{current_month}月")
    
    # 测试保存设备月报
    logger.info(f"生成并保存设备 {camera_id} 的月报 ({current_year}年{current_month}月)")
    report_id = save_monthly_report(camera_id, current_year, current_month)
    
    if report_id:
        logger.info(f"设备月报已保存，ID: {report_id}")
        
        # 测试获取设备月报
        logger.info(f"获取设备 {camera_id} 的月报 ({current_year}年{current_month}月)")
        report_data = get_monthly_report(camera_id, current_year, current_month)
        
        if report_data:
            logger.info(f"月报ID: {report_data['id']}")
            logger.info(f"设备名称: {report_data['device_name']}")
            logger.info(f"正常记录数: {report_data['normal_count']}")
            logger.info(f"告警记录数: {report_data['warning_count']}")
            logger.info(f"状态转变记录数: {report_data['transition_count']}")
            logger.info(f"创建时间: {report_data['created_at']}")
            logger.info(f"更新时间: {report_data['updated_at']}")
            logger.info(f"全文总结: {report_data['summary_analysis'][:100] if report_data['summary_analysis'] else 'None'}...")
        
        # 测试列出设备月报
        logger.info("列出最近的设备月报")
        reports = list_monthly_reports(limit=5)
        for report in reports:
            logger.info(f"- {report['report_name']} (ID: {report['id']}, 创建时间: {report['created_at']})")
    else:
        logger.error("设备月报生成失败")
    
    logger.info("=" * 50)
    
    # 测试场景月报数据库操作
    # 首先获取场景映射，使用真实的场景ID
    logger.info("获取设备场景映射关系")
    scene_mapping = get_device_scene_mapping()
    
    # 选择第一个有设备的场景进行测试
    test_scene_id = None
    test_scene_name = None
    for scene_id, scene_info in scene_mapping.items():
        devices = scene_info.get("devices", [])
        if devices:  # 确保场景下有设备
            test_scene_id = scene_id
            test_scene_name = scene_info.get("scene_name", f"场景{scene_id[:8]}")
            logger.info(f"选择场景 {scene_id} ({test_scene_name}) 进行测试: {len(devices)} 台设备")
            break
    
    if not test_scene_id:
        logger.warning("没有找到可用的场景进行测试")
        test_scene_id = "4d20d044f41a11efa38d0242ac120002"  # 使用您提供的示例场景ID
        logger.info(f"使用默认场景ID进行测试: {test_scene_id}")
    
    logger.info(f"测试场景月报功能，场景ID: {test_scene_id}, 年月: {current_year}年{current_month}月")
    
    # 测试保存场景月报
    logger.info(f"生成并保存场景 {test_scene_id} ({test_scene_name}) 的月报 ({current_year}年{current_month}月)")
    test_scene_devices = scene_mapping[test_scene_id]["devices"] if test_scene_id in scene_mapping else None
    scene_report_id = save_scene_monthly_report(test_scene_id, test_scene_name, test_scene_devices, current_year, current_month)
    
    if scene_report_id:
        logger.info(f"场景月报已保存，ID: {scene_report_id}")
        
        # 测试获取场景月报
        logger.info(f"获取场景 {test_scene_id} 的月报 ({current_year}年{current_month}月)")
        scene_report_data = get_scene_monthly_report(scene_id, current_year, current_month)
        
        if scene_report_data:
            logger.info(f"场景月报ID: {scene_report_data['id']}")
            logger.info(f"场景名称: {scene_report_data['scene_name']}")
            logger.info(f"设备数量: {scene_report_data['device_count']}")
            logger.info(f"总正常记录数: {scene_report_data['total_normal_count']}")
            logger.info(f"总告警记录数: {scene_report_data['total_warning_count']}")
            logger.info(f"总状态转变记录数: {scene_report_data['total_transition_count']}")
            logger.info(f"创建时间: {scene_report_data['created_at']}")
            logger.info(f"更新时间: {scene_report_data['updated_at']}")
            logger.info(f"场景总结: {scene_report_data['summary_analysis'][:100] if scene_report_data['summary_analysis'] else 'None'}...")
        
        # 测试列出场景月报
        logger.info("列出最近的场景月报")
        scene_reports = list_scene_monthly_reports(limit=5)
        for report in scene_reports:
            logger.info(f"- {report['report_name']} (ID: {report['id']}, 设备数: {report['device_count']}, 创建时间: {report['created_at']})")
    else:
        logger.error("场景月报生成失败")


