import json
import time
from datetime import datetime
import paho.mqtt.client as mqtt
import yaml
import os
import sys

# 添加当前工作目录到系统路径
sys.path.append(os.getcwd())  # 添加当前工作目录到系统路径

# 加载配置文件
from config_file import config

# MQTT配置
mqtt_config = config.env['mqtt']

# 定义MQTT配置字段
MQTT_BROKER = mqtt_config['broker']
MQTT_PORT = mqtt_config['port']
MQTT_TOPIC = mqtt_config['topic']
MQTT_USERNAME = mqtt_config['auth']['username']
MQTT_PASSWORD = mqtt_config['auth']['password']
MQTT_CLIENT_ID = mqtt_config['auth']['client_id']

# 定义连接回调函数
def on_connect(client, userdata, flags, rc):
    if rc == 0:
        print("MQTT连接成功！")
    else:
        print("MQTT连接失败，请检查配置")

# 定义消息发布回调函数
def on_publish(client, userdata, mid):
    print(f"消息 {mid} 已发布。")

# 创建MQTT消息内容
def create_message(device_name, foam_area):
    current_time = int(datetime.now().timestamp() * 1000)  # 转换为毫秒时间戳
    message = {
        "params": {
            "foam_area": {
                "value": round(float(foam_area), 4),
                "time": current_time
            }
        },
        "id": "304483",
        "version": "1.0",
        "productKey": "gOfvBswn7ph",
        "deviceName": device_name,
        "method": "thing.event.property.post"
    }
    return json.dumps(message, ensure_ascii=False)

# 发布泡沫检测数据
def publish_foam_data(device_name, foam_area, max_retries=3):
    for attempt in range(max_retries):
        try:
            client = mqtt.Client(client_id=f"{MQTT_CLIENT_ID}_{int(time.time())}")
            client.username_pw_set(MQTT_USERNAME, MQTT_PASSWORD)
            client.on_connect = on_connect
            client.on_publish = on_publish
            client.connect(MQTT_BROKER, MQTT_PORT, keepalive=10)
            message = create_message(device_name, foam_area)
            print(f"尝试发送消息 (第{attempt + 1}次): {message}")
            result = client.publish(MQTT_TOPIC, message, qos=1)
            result.wait_for_publish(timeout=5.0)
            client.disconnect()
            print(f"MQTT消息发送成功: {device_name} - {foam_area}")
            return True
        except Exception as e:
            print(f"MQTT发送失败 (第{attempt + 1}次): {str(e)}")
            if attempt == max_retries - 1:
                print(f"达到最大重试次数 ({max_retries})，放弃发送")
                return False
            time.sleep(1)

# 主程序
if __name__ == "__main__":
    try:
        while True:
            publish_foam_data("gze9001", 75.5)
            publish_foam_data("lc007", 75.5)
            publish_foam_data("lc008", 75.5)
            time.sleep(5)
    except KeyboardInterrupt:
        print("程序退出...")