import logging
import cv2
from pathlib import Path
from typing import Dict, List, Tuple, Any

from server.utils.handlers.base_handler import BaseFrameHandler


class MossHandler(BaseFrameHandler):
    """青苔处理器类
    
    负责处理青苔检测相关的视频帧分析
    """
    
    def process_frame(self, frame, frame_count, save_dir, camera_id, sensor_data,
                      threshold, system_type, standard_image_path=None, current_time=None) -> Tuple:
        """处理青苔检测相关的视频帧
        
        Args:
            frame: 视频帧图像数据
            frame_count: 帧计数
            save_dir: 保存目录
            camera_id: 摄像头ID
            sensor_data: 传感器数据
            threshold: 覆盖率阈值
            system_type: 系统类型
            standard_image_path: 标准图片路径
            current_time: 当前时间
            
        Returns:
            tuple: (覆盖率, 分析结果, 警报状态, 是否异常, 图片路径, 分析建议, 故障类型列表)
        """
        logging.info(f"青苔处理器开始处理帧 - 摄像头ID: {camera_id}, 帧计数: {frame_count}")
        
        # frame_filename = f"frame_{camera_id}_{current_time.strftime('%Y_%m_%d_%H_%M_%S')}.jpg"
        # frame_path = save_dir / frame_filename
        resized_frame, frame_path = self._prepare_frame(frame, save_dir, camera_id, current_time)
        failure_reasons_type = []  # 出现故障的类型
        
        # 调整图像尺寸为1600x900
        # resized_frame = cv2.resize(frame, (1600, 900), interpolation=cv2.INTER_AREA)
        
        # 保存调整后的帧
        # cv2.imwrite(str(frame_path), resized_frame)
        
        # 处理图像比较
        response_dict = self._process_image_comparison(
            resized_frame, frame_path, standard_image_path, system_type
        )
        
        # 判断是否为青苔
        value = response_dict['是否为青苔']
        if value == '是':
            coverage_float = 99
            failure_reasons_type.append('出现青苔')
        else:
            coverage_float = 1
            
        analysis_result = response_dict.get('处理建议', '识别到青苔,正在处理中...')
        all_situation_analysis = response_dict.get('图片分析结果', '')
        
        # 确定警报状态
        alarm_status_flag, is_abnormal = self._determine_alarm_status(coverage_float, threshold)
        
        logging.info(f"青苔处理器处理完成 - 摄像头ID: {camera_id}, 是否为青苔: {value}, 警报状态: {alarm_status_flag}")
        if failure_reasons_type:
            logging.warning(f"检测到故障类型: {', '.join(failure_reasons_type)}")
        
        return (coverage_float, all_situation_analysis, alarm_status_flag, is_abnormal,
                str(frame_path), analysis_result, failure_reasons_type) 