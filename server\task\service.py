"""
数据库服务端模块

主要功能：
1. 提供数据库cameras和frame_analysis的数据插入
2. 维护frame_analysis_latest表中每个设备的最新记录
3. 提供数据清理和维护功能
"""
import os
import sys
import logging
sys.path.append(os.getcwd())
from datetime import datetime
from pathlib import Path
import json  # 添加json模块导入

import yaml
from config_file import config
from server.utils.pg_tool import connect_db
from server.utils.logger import setup_logging

# 配置日志
setup_logging()
logger = logging.getLogger(__name__)

# sys.path.append(os.getcwd())
from llms.vllm_api_server import process_image

import time
from datetime import date


def determine_coverage_level(coverage_float):
    """根据泡沫覆盖率确定覆盖等级

    Args:
        coverage_float (float): 泡沫覆盖率（0.0 到 1.0 之间的浮点数）

    Returns:
        str: 覆盖等级（LOW、MEDIUM、HIGH）

    配置说明：
        从配置文件读取阈值和等级设置，如果配置读取失败则使用默认值：
        - LOW: 0-25%
        - MEDIUM: 26-50%
        - HIGH: 51-100%
    """
    try:
        # config = load_config()
        config = config.env
        thresholds = config['coverage_levels']['thresholds']
        levels = config['coverage_levels']['levels']

        if 0 <= coverage_float <= thresholds['low']:
            return levels['low']
        elif coverage_float <= thresholds['medium']:
            return levels['medium']
        elif coverage_float <= thresholds['high']:
            return levels['high']
        else:
            raise ValueError("覆盖率值超出有效范围 (0-100%)")
    except Exception as e:
        logger.warning(f"获取覆盖等级配置失败: {e}")
        # 使用默认值作为后备方案
        if 0 <= coverage_float <= 25:
            return "LOW"
        elif 26 <= coverage_float <= 50:
            return "MEDIUM"
        elif 51 <= coverage_float <= 100:
            return "HIGH"
        else:
            raise ValueError("覆盖率值超出有效范围 (0-100%)")



def insert_single_test_data(camera_id, video_id, frame_number, timestamp, frame_path,
                            coverage_rate, coverage_level, alarm_status, analysis_detail,
                            is_abnormal, do_value, mlss_value, adjustment_suggestion,
                            failure_reasons_type,alarmtype # 新增加字段failure_reasons_type
                            ):
    """插入单条分析数据

    Args:
        camera_id (str): 摄像头ID
        video_id (str): 视频ID
        frame_number (int): 帧号
        timestamp (datetime): 时间戳
        frame_path (str): 帧图片路径
        coverage_rate (float): 覆盖率
        coverage_level (str): 覆盖等级
        alarm_status (str): 报警状态
        analysis_detail (str): 分析详情
        is_abnormal (bool): 是否异常
        do_value (float): DO值
        mlss_value (float): MLSS值
        adjustment_suggestion (str): 调整建议
        # failure_reasons_type  # 故障原因(新增加的,需要在数据库中增加字段以后才有效)

    """
    conn = connect_db()
    if not conn:
        return
    try:
        cur = conn.cursor()
        # 将failure_reasons_type转换为JSON格式
        failure_reasons_number = len(failure_reasons_type)
        failure_reasons_json = json.dumps(failure_reasons_type)
        
        # 准备单条测试数据
        test_data = (
            camera_id,  # 摄像头ID
            video_id,  # 视频ID
            frame_number,  # 帧号
            timestamp,  # 时间戳
            frame_path,  # 帧图片路径
            coverage_rate,  # 覆盖率
            coverage_level,  # 覆盖等级
            alarm_status,  # 报警状态
            analysis_detail,  # 分析详情
            is_abnormal,  # 是否异常
            do_value,  # DO值
            mlss_value,  # MLSS值
            adjustment_suggestion,  # 调整建议
            failure_reasons_json,  # 转换为JSON格式的故障原因
            failure_reasons_number,  # 故障原因数量
            alarmtype,  # 报警类型
        )

        # 更新SQL插入语句，添加 do_value 和 mlss_value 字段
         # 新增加字段failure_reasons_type,设置完成数据库字段需要添加上
        insert_sql = """
        INSERT INTO frame_analysis (
            camera_id, video_id, frame_number, timestamp, frame_path,
            coverage_rate, coverage_level, alarm_status, analysis_detail,
            is_abnormal, do_value, mlss_value, adjustment_suggestion,failure_reasons_type,failure_reasons_number,
            alarmtype
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,%s,%s,%s)
        """

        # 执行单条插入
        cur.execute(insert_sql, test_data)
        conn.commit()
        logger.info("成功插入一条测试数据")

    except Exception as e:
        logger.error(f"插入数据错误: {e}")
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()


def get_data_statistics():
    """获取数据统计信息

    统计内容：
    1. 总记录数
    2. 最早记录时间
    3. 最新记录时间
    4. 3个月前的记录数量
    """
    conn = connect_db()
    if not conn:
        return

    try:
        cur = conn.cursor()

        # 查询数据统计信息
        stats_sql = """
        SELECT 
            COUNT(*) as total_records,
            MIN(timestamp) as oldest_record,
            MAX(timestamp) as newest_record,
            COUNT(CASE WHEN timestamp < NOW() - INTERVAL '3 months' THEN 1 END) as old_records
        FROM frame_analysis;
        """

        cur.execute(stats_sql)
        stats = cur.fetchone()

        logger.info("=== 数据统计信息 ===")
        logger.info(f"总记录数: {stats[0]}")
        logger.info(f"最早记录时间: {stats[1]}")
        logger.info(f"最新记录时间: {stats[2]}")
        logger.info(f"3个月前的记录数: {stats[3]}")

    except Exception as e:
        logger.error(f"获取统计信息错误: {e}")
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()


def is_off_peak_hours():
    """检查当前是否是低峰时段

    Returns:
        bool: True表示当前是低峰时段（凌晨2点到5点）
    """
    current_hour = datetime.now().hour
    return 2 <= current_hour <= 5


def cleanup_old_frame_analysis_data_smart(months=6, cleanup_day=15):
    """智能清理历史帧分析数据

    特点：
    1. 只在指定日期（默认每月15号）执行
    2. 只在系统低峰期（凌晨2-5点）执行
    3. 分批次删除数据，避免系统压力

    Args:
        months (int): 保留最近几个月的数据，默认6个月
        cleanup_day (int): 每月清理的日期，默认15号

    Returns:
        int: 清理的记录数量
    """
    conn = None
    cur = None

    try:
        conn = connect_db()
        if not conn:
            return 0

        # 检查是否是指定清理日期
        today = date.today()
        if today.day != cleanup_day:
            logger.info(f"今天不是指定的清理日期（每月{cleanup_day}号），跳过清理")
            return 0

        # 检查是否是低峰时段
        if not is_off_peak_hours():
            logger.info("当前不是系统低峰期（凌晨2点到5点），跳过清理")
            return 0

        cur = conn.cursor()

        # 分批次删除数据，避免一次性删除太多数据
        batch_size = 1000  # 每批删除的记录数
        total_deleted = 0

        while True:
            # 修改删除语句，使用正确的主键列名
            delete_sql = """
            WITH to_delete AS (
                SELECT id
                FROM frame_analysis 
                WHERE timestamp < NOW() - INTERVAL '%s months'
                LIMIT %s
            )
            DELETE FROM frame_analysis
            WHERE id IN (SELECT id FROM to_delete)
            RETURNING id;
            """

            cur.execute(delete_sql, (months, batch_size))
            deleted_count = cur.rowcount

            if deleted_count == 0:
                break

            total_deleted += deleted_count
            conn.commit()

            logger.info(f"已清理 {total_deleted} 条历史数据...")

            # 每批次之间稍作暂停，减少数据库压力
            time.sleep(1)

        if total_deleted > 0:
            logger.info(f"清理完成！共删除 {total_deleted} 条历史数据（{months}个月前的数据）")
            logger.info(f"清理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

            # 可选：执行VACUUM回收空间
            if total_deleted > 10000:  # 如果删除的数据量较大，执行VACUUM
                logger.info("开始回收存储空间...")
                cur.execute("VACUUM ANALYZE frame_analysis;")
                logger.info("存储空间回收完成")
        else:
            logger.info("没有需要清理的历史数据")

        return total_deleted

    except Exception as e:
        logger.error(f"清理历史数据错误: {e}")
        return 0
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()


def upsert_latest_analysis(camera_id, video_id, frame_number, timestamp, frame_path,
                           coverage_rate, coverage_level, alarm_status, analysis_detail,
                           is_abnormal, do_value, mlss_value, adjustment_suggestion,
                           failure_reasons_type,alarmtype # 新增加字段failure_reasons_type
                           ):
    """插入或更新最新的分析结果

    使用UPSERT操作确保frame_analysis_latest表中只保留每个摄像头的最新记录

    Args:
        (参数说明同insert_single_test_data函数)
    """
    conn = connect_db()
    if not conn:
        return

    try:
        cur = conn.cursor()
        failure_reasons_number = len(failure_reasons_type)
        # 将failure_reasons_type转换为JSON格式
        failure_reasons_json = json.dumps(failure_reasons_type)
        
        # 首先，查询当前是否存在未读的报警，如果存在，则保持报警状态
        query_sql = """
        SELECT is_read, alarm_status, failure_reasons_type, failure_reasons_number, alarmtype
        FROM frame_analysis_latest
        WHERE camera_id = %s
        """
        cur.execute(query_sql, (camera_id,))
        existing_record = cur.fetchone()
        
        # 如果存在记录且是未读报警(is_read=1)且当前是正常状态(alarm_status='NO_ALARM')
        # 则保持原有的报警状态，不覆盖
        original_is_read = None
        if existing_record and existing_record[0] == '1':
            # 当前是正常状态但已有未读报警，保持报警状态
            original_is_read = existing_record[0]  # 已读状态
        
        # 确定is_read值
        if original_is_read is not None:
            is_read = original_is_read  # 保持原有的已读状态
        elif coverage_level != 'LOW':
            is_read = 1  # 未读状态
        else:
            is_read = 0  # 已读状态
            
        # 使用UPSERT语法（INSERT ... ON CONFLICT）
        upsert_sql = """
        INSERT INTO frame_analysis_latest (
            camera_id, video_id, frame_number, timestamp, frame_path,
            coverage_rate, coverage_level, alarm_status, analysis_detail,
            is_abnormal, do_value, mlss_value, adjustment_suggestion,failure_reasons_type,failure_reasons_number,
            is_read, alarmtype
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,%s,%s,%s,%s)
        ON CONFLICT (camera_id) DO UPDATE SET
            video_id = EXCLUDED.video_id,
            frame_number = EXCLUDED.frame_number,
            timestamp = EXCLUDED.timestamp,
            frame_path = EXCLUDED.frame_path,
            coverage_rate = EXCLUDED.coverage_rate,
            coverage_level = EXCLUDED.coverage_level,
            alarm_status = EXCLUDED.alarm_status,
            analysis_detail = EXCLUDED.analysis_detail,
            is_abnormal = EXCLUDED.is_abnormal,
            do_value = EXCLUDED.do_value,
            mlss_value = EXCLUDED.mlss_value,
            adjustment_suggestion = EXCLUDED.adjustment_suggestion,
            failure_reasons_type = EXCLUDED.failure_reasons_type,
            failure_reasons_number = EXCLUDED.failure_reasons_number,
            is_read = EXCLUDED.is_read,
            alarmtype = EXCLUDED.alarmtype
        """

        # 准备 SQL 值列表
        values_list = [
            camera_id, video_id, frame_number, timestamp, frame_path,
            coverage_rate, coverage_level, alarm_status, analysis_detail,
            is_abnormal, do_value, mlss_value, adjustment_suggestion,
            failure_reasons_json, failure_reasons_number
        ]
        
        # 添加is_read值
        values_list.append(is_read)
            
        # 添加 alarmtype 值    
        values_list.append(alarmtype)
            
        values = tuple(values_list)

        cur.execute(upsert_sql, values)
        conn.commit()
        logger.info(f"成功更新摄像头 {camera_id} 的最新分析结果")

    except Exception as e:
        logger.error(f"更新数据错误: {e}")
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()


def get_latest_analysis_by_camera(camera_id):
    """根据摄像头ID查询最新的分析结果

    Args:
        camera_id (str): 摄像头ID

    Returns:
        dict: 包含最新分析结果的字典，包括：
            - camera_id: 摄像头ID
            - video_id: 视频ID
            - frame_number: 帧号
            - timestamp: 时间戳
            - frame_path: 帧图片路径
            - coverage_rate: 覆盖率
            - coverage_level: 覆盖等级
            - alarm_status: 报警状态
            - analysis_detail: 分析详情
            - is_abnormal: 是否异常
            - do_value: DO值
            - mlss_value: MLSS值
            - adjustment_suggestion: 调整建议
            - failure_reasons_type: 故障原因类型
            - failure_reasons_number: 故障原因数量
            - is_read: 是否已读（1表示已读，NULL表示未读）
            - alarmtype: 报警类型
        None: 未找到记录时返回None
    """
    conn = connect_db()
    if not conn:
        return None

    try:
        cur = conn.cursor()
        query_sql = """
        SELECT 
            camera_id, video_id, frame_number, timestamp, frame_path,
            coverage_rate, coverage_level, alarm_status, analysis_detail,
            is_abnormal, do_value, mlss_value, adjustment_suggestion,
            failure_reasons_type, failure_reasons_number, is_read, alarmtype
        FROM frame_analysis_latest 
        WHERE camera_id = %s;
        """

        cur.execute(query_sql, (camera_id,))
        result = cur.fetchone()

        if result:
            # 将查询结果转换为字典格式
            result_dict = {
                'camera_id': result[0],
                'video_id': result[1],
                'frame_number': result[2],
                'timestamp': result[3],
                'frame_path': result[4],
                'coverage_rate': result[5],
                'coverage_level': result[6],
                'alarm_status': result[7],
                'analysis_detail': result[8],
                'is_abnormal': result[9],
                'do_value': result[10],
                'mlss_value': result[11],
                'adjustment_suggestion': result[12],
                'failure_reasons_type': result[13],
                'failure_reasons_number': result[14],
                'is_read': result[15],
                'alarmtype': result[16]
            }
            return result_dict
        else:
            logger.warning(f"未找到摄像头 {camera_id} 的分析记录")
            return None

    except Exception as e:
        logger.error(f"查询数据错误: {e}")
        return None
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()


if __name__ == '__main__':
    # 修改测试代码
    # threshold = 60.0
    # response_dict = process_image('assets/frame.jpg', threshold)

    # coverage_float = float(response_dict['当前泡沫覆盖率'].strip('%'))
    # if coverage_float > threshold:
    #     alarm_status = '超出阈值'
    #     is_abnormals = True
    # else:
    #     alarm_status = '正常'
    #     is_abnormals = False

    # 示例故障原因列表
    failure_reasons = ["设备故障", "泡沫异常"]
    
    # 根据 failure_reasons 的长度设置 alarmtype
    if len(failure_reasons) > 0:
        alarmtype = '运行故障,#f05454'
    else:
        alarmtype = ''
    
    # 更新测试数据，添加 DO 和 MLSS 值
    test_data2 = (
        '4049',  # 摄像头ID
        '0001',  # 视频ID
        3,  # 帧号
        datetime.now(),  # 时间戳
        'assets/frame.jpg',  # 帧图片路径
        48,  # 覆盖率
        'LOW',  # 覆盖等级
        '正常',  # 报警
        '测试分析详情',  # 分析详情
        False,  # 异常状态
        5.6,  # DO值（示例值）
        2000.0,  # MLSS值（示例值）
        '测试调整建议',  # 调整建议
        failure_reasons,  # 故障原因列表
        alarmtype  # 报警类型
    )

    # **************** 在历史数据表中插入测试数据 *************** #

    # insert_single_test_data(*test_data2)

    # ********* 测试在新的表中保留同一个camera_id的最新数据 ********* #

    upsert_latest_analysis(*test_data2)

    # ************** 测试对前3个月的分析记录进行删除 ************** #

    # print("清理前的数据统计：")
    # get_data_statistics()

    # # 测试智能清理功能
    # print("\n=== 测试智能清理功能 ===")

    # # 设置清理参数
    # CLEANUP_MONTHS = 3    # 保留最近3个月的数据
    # CLEANUP_DAY = 15      # 每月15号进行清理

    # # 显示清理设置
    # print(f"""
    # 清理设置：
    # - 保留最近{CLEANUP_MONTHS}个月的数据
    # - 每月{CLEANUP_DAY}号进行清理
    # - 仅在凌晨2点到5点的低峰期执行
    # - 分批次删除数据，每批{1000}条
    # """)

    # # 执行智能清理
    # deleted_count = cleanup_old_frame_analysis_data_smart(
    #     months=CLEANUP_MONTHS,
    #     cleanup_day=CLEANUP_DAY
    # )

    # **************** 测试查询最新数据 **************** #
    # test_camera_id = 'cam_002'
    # latest_data = get_latest_analysis_by_camera(test_camera_id)

    # if latest_data:
    #     print("\n=== 最新分析结果 ===")
    #     print(f"摄像头ID: {latest_data['camera_id']}")
    #     print(f"覆盖率: {latest_data['coverage_rate']}%")
    #     print(f"覆盖等级: {latest_data['coverage_level']}")
    #     print(f"报警状态: {latest_data['alarm_status']}")
    #     print(f"DO值: {latest_data['do_value']}")
    #     print(f"MLSS值: {latest_data['mlss_value']}")
    #     print(f"分析时间: {latest_data['timestamp']}")
    #     print(f"分析详情: {latest_data['analysis_detail']}")
    #     print(f"调整建议: {latest_data['adjustment_suggestion']}")
    #     print(f"故障原因: {latest_data['failure_reasons_type']}")
    #     print(f"故障原因数量: {latest_data['failure_reasons_number']}")
    #     print(f"是否已读: {latest_data['is_read']}")
