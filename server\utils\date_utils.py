"""
日期时间工具模块

提供统一的日期时间格式化和处理功能
"""

from datetime import datetime
from typing import Union, Optional
import re
import logging

# 获取项目配置的日志记录器
logger = logging.getLogger(__name__)


def validate_datetime_string(dt_str: str) -> bool:
    """
    验证时间字符串格式是否正确
    
    Args:
        dt_str: 时间字符串
        
    Returns:
        bool: 格式正确返回True，否则返回False
    """
    if not isinstance(dt_str, str):
        return False
    
    # 验证 'YYYY-MM-DD HH:MM:SS' 格式
    pattern = r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$'
    return bool(re.match(pattern, dt_str))


def validate_date_string(dt_str: str) -> bool:
    """
    验证日期字符串格式是否正确
    
    Args:
        dt_str: 日期字符串
        
    Returns:
        bool: 格式正确返回True，否则返回False
    """
    if not isinstance(dt_str, str):
        return False
    
    # 验证 'YYYY-MM-DD' 格式
    pattern = r'^\d{4}-\d{2}-\d{2}$'
    return bool(re.match(pattern, dt_str))


def format_datetime_string(dt_obj: Union[datetime, str, None]) -> Optional[str]:
    """
    将datetime对象格式化为标准字符串格式
    
    Args:
        dt_obj: datetime对象、已格式化的字符串或None
        
    Returns:
        str: 格式化后的时间字符串 'YYYY-MM-DD HH:MM:SS'，如果输入为None或格式化失败则返回None
        
    Examples:
        >>> dt = datetime(2025, 8, 8, 14, 30, 0)
        >>> format_datetime_string(dt)
        '2025-08-08 14:30:00'
        
        >>> format_datetime_string('2025-08-08 14:30:00')
        '2025-08-08 14:30:00'
        
        >>> format_datetime_string('invalid_string')
        None
        
        >>> format_datetime_string(None)
        None
    """
    if dt_obj is None:
        return None
    
    try:
        if isinstance(dt_obj, datetime):
            return dt_obj.strftime('%Y-%m-%d %H:%M:%S')
        
        # 如果是字符串，验证格式后返回
        if isinstance(dt_obj, str):
            if validate_datetime_string(dt_obj):
                return dt_obj
            else:
                logger.warning(f"无效的时间字符串格式: {dt_obj}")
                return None
        
        # 其他类型记录警告并返回None
        logger.warning(f"不支持的数据类型用于时间格式化: {type(dt_obj)}")
        return None
        
    except Exception as e:
        logger.error(f"时间格式化失败: {dt_obj}, 错误: {str(e)}")
        return None


def format_date_string(dt_obj: Union[datetime, str, None]) -> Optional[str]:
    """
    将datetime对象格式化为日期字符串格式
    
    Args:
        dt_obj: datetime对象、已格式化的字符串或None
        
    Returns:
        str: 格式化后的日期字符串 'YYYY-MM-DD'，如果输入为None或格式化失败则返回None
        
    Examples:
        >>> dt = datetime(2025, 8, 8, 14, 30, 0)
        >>> format_date_string(dt)
        '2025-08-08'
        
        >>> format_date_string('2025-08-08')
        '2025-08-08'
        
        >>> format_date_string('invalid_date')
        None
    """
    if dt_obj is None:
        return None
    
    try:
        if isinstance(dt_obj, datetime):
            return dt_obj.strftime('%Y-%m-%d')
        
        # 如果是字符串，验证格式后返回
        if isinstance(dt_obj, str):
            if validate_date_string(dt_obj):
                return dt_obj
            else:
                logger.warning(f"无效的日期字符串格式: {dt_obj}")
                return None
        
        # 其他类型记录警告并返回None
        logger.warning(f"不支持的数据类型用于日期格式化: {type(dt_obj)}")
        return None
        
    except Exception as e:
        logger.error(f"日期格式化失败: {dt_obj}, 错误: {str(e)}")
        return None


def format_report_data_timestamps(data_dict: dict, datetime_fields: list = None) -> dict:
    """
    批量格式化字典中的datetime字段
    
    Args:
        data_dict: 包含datetime字段的字典
        datetime_fields: 需要格式化的字段名列表，默认为['created_at', 'updated_at']
        
    Returns:
        dict: 格式化后的字典（原地修改）
        
    Examples:
        >>> data = {'created_at': datetime.now(), 'updated_at': datetime.now(), 'name': 'test'}
        >>> format_report_data_timestamps(data)
        {'created_at': '2025-08-08 14:30:00', 'updated_at': '2025-08-08 14:30:00', 'name': 'test'}
    """
    if not isinstance(data_dict, dict):
        logger.warning(f"format_report_data_timestamps 期望字典类型，但收到: {type(data_dict)}")
        return data_dict
    
    if datetime_fields is None:
        datetime_fields = ['created_at', 'updated_at']
    
    try:
        for field in datetime_fields:
            if field in data_dict and data_dict[field] is not None:
                formatted_value = format_datetime_string(data_dict[field])
                if formatted_value is not None:
                    data_dict[field] = formatted_value
                else:
                    logger.warning(f"字段 {field} 格式化失败，保持原值: {data_dict[field]}")
    except Exception as e:
        logger.error(f"批量格式化字典时间字段失败: {str(e)}")
    
    return data_dict


def format_report_list_timestamps(data_list: list, datetime_fields: list = None) -> list:
    """
    批量格式化列表中每个字典的datetime字段
    
    Args:
        data_list: 包含字典的列表
        datetime_fields: 需要格式化的字段名列表，默认为['created_at', 'updated_at']
        
    Returns:
        list: 格式化后的列表（原地修改）
    """
    if not isinstance(data_list, list):
        logger.warning(f"format_report_list_timestamps 期望列表类型，但收到: {type(data_list)}")
        return data_list
    
    if datetime_fields is None:
        datetime_fields = ['created_at', 'updated_at']
    
    try:
        for data_dict in data_list:
            if isinstance(data_dict, dict):
                format_report_data_timestamps(data_dict, datetime_fields)
            else:
                logger.warning(f"列表中包含非字典元素: {type(data_dict)}")
    except Exception as e:
        logger.error(f"批量格式化列表时间字段失败: {str(e)}")
    
    return data_list
