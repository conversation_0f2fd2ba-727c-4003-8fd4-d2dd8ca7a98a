import logging
import time
from typing import Optional, Dict

from server.remote.point import APIClient

"""
传感器管理模块

主要功能：
1. 获取传感器数据
2. 处理数据获取失败的重试机制
3. 格式化传感器数据
"""


class SensorManager:
    """传感器管理器类
    
    负责管理传感器数据的获取和处理
    
    Attributes:
        api_client (APIClient): API客户端实例
        max_retries (int): 最大重试次数
        retry_delay (float): 重试间隔时间（秒）
    """

    def __init__(self, config: dict):
        """初始化传感器管理器
        
        Args:
            config (dict): 配置字典，包含：
                - system.retry.max_retries: 最大重试次数
                - system.retry.delay: 重试间隔时间
        """
        self.api_client = APIClient()
        self.max_retries = config['system']['retry']['max_retries']
        self.retry_delay = config['system']['retry']['delay']

    def get_sensor_data(self, camera_id: str) -> Optional[Dict]:
        """获取传感器数据
        
        尝试获取传感器数据，如果失败则进行重试
        
        Args:
            camera_id (str): 摄像头ID
            
        Returns:
            Optional[Dict]: 传感器数据字典，包含：
                - DO: 溶解氧值
                - MLSS: 混合液悬浮固体浓度
                - bubble_area: 气泡面积
                如果获取失败返回None
        """
        try:
            return self._fetch_sensor_data(camera_id)
        except Exception as e:
            logging.error(f"获取传感器数据时发生错误: {str(e)}")
            return self._retry_fetch_sensor_data(camera_id)

    def _fetch_sensor_data(self, camera_id: str) -> Optional[Dict]:
        """获取单次传感器数据
        
        从API获取传感器数据并格式化
        
        Args:
            camera_id (str): 摄像头ID
            
        Returns:
            Optional[Dict]: 格式化后的传感器数据
                - DO: 溶解氧值（'-'表示无数据）
                - MLSS: 混合液悬浮固体浓度（'-'表示无数据）
                - bubble_area: 气泡面积（'-'表示无数据）
        """
        raw_data = self.api_client.get_device_points(
            device_id=camera_id,
            bind_status="1",
            query_value="true"
        )

        formatted_data = self.api_client.format_point_data(raw_data)
        if not formatted_data:
            logging.error(f"摄像头 {camera_id} 未获取到有效的传感器数据")
            return None

        return {
            'DO': formatted_data.get('DO', '-'),
            'MLSS': formatted_data.get('MLSS', '-'),
            'bubble_area': '-'
        }

    def _retry_fetch_sensor_data(self, camera_id: str) -> Optional[Dict]:
        """重试获取传感器数据
        
        在初次获取失败后进行多次重试
        
        Args:
            camera_id (str): 摄像头ID
            
        Returns:
            Optional[Dict]: 传感器数据，重试全部失败时返回None
            
        重试策略：
        1. 最大重试次数由配置决定
        2. 每次重试之间有固定延迟
        3. 记录每次重试的错误信息
        """
        for attempt in range(self.max_retries - 1):
            try:
                time.sleep(self.retry_delay)
                return self._fetch_sensor_data(camera_id)
            except Exception as retry_e:
                logging.error(f"重试 {attempt + 1} 失败: {str(retry_e)}")
        return None
