# -------------------------------------------------------- #
#                       用于根据识别的结果反控设备                      #
# -------------------------------------------------------- #
# 获取设备列表
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
import json
import os
from dataclasses import dataclass
from typing import Dict
import re
import requests
import yaml
import logging

from config_file import config
from server.utils.logger import setup_logging
from server.utils.get_cameras_info import get_robot_status_info

# 配置日志
setup_logging()
logger = logging.getLogger(__name__)

def control_device(device_type: str, mode: str = "", action: str = "", device_id: int = 0) -> Dict:
    """通用设备控制函数

    Args:
        device_type: 设备类型（robot-机器人/slag_pipe-撇渣管/slag_weir_gate-排渣堰门/sleeve_valve-套筒阀）
        mode: 控制模式，默认为""
        action: 控制命令执行动作
            - 机器人:
                - stop_and_restart_with_speed-设置速度并重启
                - move_forward-前进
                - move_backward-后退
                - stop_movement-停止
                - start_brush-启动刷子
                - stop_brush-停止刷子
            - 套筒阀: open_valve-打开阀门/close_valve-关闭阀门
            - 撇渣管: open_valve-打开/close_valve-关闭
            - 排渣堰门: open_gate-打开/close_gate-关闭
        device_id: 设备ID

    Returns:
        Dict: 包含设备控制状态信息的字典
    """
    logger.info(f"开始控制设备: 类型={device_type}, 模式={mode}, 动作={action}, 设备ID={device_id}")
    
    # 首先检查机器人的AI控制状态
    try:
        robot_status = get_robot_status_info(device_id)
        logger.info(f"获取到设备状态信息: 设备ID={device_id}, 状态数据={json.dumps(robot_status, ensure_ascii=False)}")
        
        # 根据设备类型获取对应的AI控制状态
        ai_control_status = 0  # 默认为0，不允许控制
        
        if device_type == "robot":
            # 机器人直接使用顶层的ai_control_status
            ai_control_status = robot_status.get("ai_control_status", 0)
            logger.info(f"机器人AI控制状态: 设备ID={device_id}, AI控制状态={ai_control_status}")
        elif device_type == "slag_pipe" and "slag_pipe" in robot_status:
            # 撇渣管使用slag_pipe下的ai_control_status
            ai_control_status = robot_status["slag_pipe"].get("ai_control_status", 0)
            logger.info(f"撇渣管AI控制状态: 设备ID={device_id}, AI控制状态={ai_control_status}, 撇渣管状态数据={json.dumps(robot_status['slag_pipe'], ensure_ascii=False)}")
        elif device_type == "slag_weir_gate" and "slag_weir_gate" in robot_status:
            # 排渣堰门使用slag_weir_gate下的ai_control_status
            ai_control_status = robot_status["slag_weir_gate"].get("ai_control_status", 0)
            logger.info(f"排渣堰门AI控制状态: 设备ID={device_id}, AI控制状态={ai_control_status}, 排渣堰门状态数据={json.dumps(robot_status['slag_weir_gate'], ensure_ascii=False)}")
        elif device_type == "sleeve_valve" and "sleeve_valve" in robot_status:
            # 套筒阀使用sleeve_valve下的ai_control_status
            ai_control_status = robot_status["sleeve_valve"].get("ai_control_status", 0)
            logger.info(f"套筒阀AI控制状态: 设备ID={device_id}, AI控制状态={ai_control_status}, 套筒阀状态数据={json.dumps(robot_status['sleeve_valve'], ensure_ascii=False)}")
        else:
            logger.warning(f"未找到设备类型 {device_type} 的AI控制状态信息: 设备ID={device_id}, 可用状态数据={json.dumps(robot_status, ensure_ascii=False)}")
        
        # 检查AI控制状态，只有当ai_control_status为1时才允许控制
        if ai_control_status != 1:
            error_msg = f"{device_type} 设备 {device_id} 的AI控制状态为 {ai_control_status}，不允许AI控制操作"
            logger.warning(error_msg)
            return {"error": error_msg, "code": 403, "msg": "AI控制未开启"}
    
        # 从配置文件获取地址和端口
        host = config.env['api']['development']['host']
        port = config.env['api']['development']['port']
        
        # 根据设备类型决定是否需要token
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json"
        }
        
        # 构建请求体
        payload = {
            "device_type": device_type,
            # "mode": mode,
            "action": action,
            "device_id": device_id
        }
        
        # 发送控制请求
        logger.info(f"发送控制请求到: http://{host}:{port}/ctrl/api/device/control")
        response = requests.post(
            f"http://{host}:{port}/ctrl/api/device/control",
            headers=headers,
            json=payload
        )
        response.raise_for_status()
        
        # 返回响应数据
        result = response.json()
        logger.info(f"设备控制请求成功: 设备类型={device_type}, 设备ID={device_id}, 动作={action}, 响应结果={json.dumps(result, ensure_ascii=False)}")
        return result
        
    except requests.exceptions.RequestException as e:
        error_msg = f"控制设备失败: 设备类型={device_type}, 设备ID={device_id}, 动作={action}, 错误信息={str(e)}"
        logger.error(error_msg, exc_info=True)
        return {"error": str(e)}
    except Exception as e:
        error_msg = f"控制设备过程中发生异常: 设备类型={device_type}, 设备ID={device_id}, 动作={action}, 错误信息={str(e)}"
        logger.error(error_msg, exc_info=True)
        return {"error": str(e)}

# 保持向后兼容的函数
def control_robot(device_type="robot", mode="", action="stop_and_restart_with_speed",device_id=0) -> Dict:
    """控制机器人设备 (兼容旧版本)

    Args:
        device_type: 设备类型，默认为"robot"
        mode: 控制模式
        device_id: 设备ID
        action: 控制命令 (stop_and_restart_with_speed/move_forward/move_backward/stop_movement/start_brush/stop_brush)

    Returns:
        Dict: 包含机器人控制状态信息的字典
    """
    return control_device(device_type, mode, action, device_id)

def control_slag_pipe(device_type="slag_pipe", mode="", action="open_valve", device_id=4051) -> Dict:
    """控制撇渣管 (兼容旧版本)

    Args:
        device_type: 设备类型，默认为"slag_pipe"
        mode: 控制模式，默认为"switch_to_ai_control"
        action: 控制命令 (open_valve/close_valve)
        device_id: 设备ID，默认为4051

    Returns:
        Dict: 包含控制状态信息的字典
    """
    return control_device(device_type, mode, action, device_id)
# 控制排渣堰门
def control_slag_weir_gate(device_type="slag_weir_gate", mode="", action="open_gate", device_id=4234) -> Dict:
    """控制排渣堰门

    Args:
        device_type: 设备类型，默认为"slag_weir_gate"
        mode: 控制模式，默认为"switch_to_ai_control"
        action: 控制命令 (open_gate/close_gate)
        device_id: 设备ID，默认为4234

    Returns:
        Dict: 包含控制状态信息的字典
    """
    return control_device(device_type, mode, action, device_id)

if __name__ == "__main__":
    # 控制撇渣管打开
    # result = control_slag_pipe(device_id=4051)
    # print(result)
    # 控制撇渣管关闭
    # result = control_slag_pipe(device_type="slag_pipe", mode="switch_to_ai_control", action="close_valve", device_id=4051)
    # print(result)
    # 识别到青苔机器人停止启动控制
    # result = control_robot(device_id=4051)
    # 控制排渣堰门打开
    result = control_slag_weir_gate(device_id=4051)
    print(result)

