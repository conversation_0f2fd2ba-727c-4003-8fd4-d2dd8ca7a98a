import cv2
import numpy as np
import os


def transform_image_with_coords(image_path, coords_file_path, save_result=False, output_path=None):
    """
    根据坐标点文件对图片进行透视变换
    
    Args:
        image_path: 输入图像路径
        coords_file_path: 坐标点文件路径
        save_result: 是否保存结果
        output_path: 输出图像路径，如果为None且save_result为True，则自动生成
    
    Returns:
        transformed_image: 变换后的图像
        output_path: 如果save_result为True，返回保存的文件路径；否则为None
    """
    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"错误: 无法读取图像 {image_path}")
        return None, None
    
    # 读取坐标点文件
    points = []
    original_image_path = None
    original_image_size = None
    
    try:
        with open(coords_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
            # 解析注释行中的原始图像路径和尺寸
            for line in lines:
                if line.startswith('# 原始图像:'):
                    original_image_path = line.replace('# 原始图像:', '').strip()
                elif line.startswith('# 原始图像尺寸'):
                    size_match = line.strip().split(': ')[1].split('x')
                    if len(size_match) == 2:
                        try:
                            original_image_size = (int(size_match[0]), int(size_match[1]))
                        except ValueError:
                            pass
            
            # 读取坐标点
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#'):
                    try:
                        x, y = map(int, line.split(','))
                        points.append((x, y))
                    except ValueError:
                        continue
    except Exception as e:
        print(f"错误: 读取坐标文件时出错 - {str(e)}")
        return None, None
    
    if len(points) < 4:
        print("错误: 坐标点数量不足，至少需要4个点")
        return None, None
    
    # 检查是否需要缩放坐标点
    scale_x = 1.0
    scale_y = 1.0
    need_scaling = False
    
    # 如果坐标文件中指定了原始图像尺寸，检查是否需要缩放
    if original_image_size:
        curr_height, curr_width = image.shape[:2]
        orig_width, orig_height = original_image_size
        
        # 如果原始图像尺寸与当前图像不同，计算缩放因子
        if orig_width != curr_width or orig_height != curr_height:
            scale_x = curr_width / orig_width
            scale_y = curr_height / orig_height
            need_scaling = True
            print(f"检测到图像尺寸不匹配，应用缩放: X={scale_x:.2f}, Y={scale_y:.2f}")
    
    # 应用缩放（如果需要）
    if need_scaling:
        points = [(int(x * scale_x), int(y * scale_y)) for x, y in points]
    
    # 将点转换为numpy数组
    src_points = np.array(points, dtype=np.float32)
    
    # 计算边界框
    min_x = min(p[0] for p in src_points)
    max_x = max(p[0] for p in src_points)
    min_y = min(p[1] for p in src_points)
    max_y = max(p[1] for p in src_points)
    
    # 计算宽度和高度
    width = max_x - min_x
    height = max_y - min_y
    
    # 创建目标矩形的四个角点
    dst_points = np.array([
        [0, 0],                # 左上
        [width, 0],            # 右上
        [width, height],       # 右下
        [0, height]            # 左下
    ], dtype=np.float32)
    
    # 根据点数选择合适的变换方法
    if len(points) == 4:
        # 计算透视变换矩阵
        matrix = cv2.getPerspectiveTransform(src_points, dst_points)
    else:
        # 多于4个点时，使用findHomography
        # 创建对应的目标点 (映射到规则矩形)
        dst_points_homography = []
        for i, src_pt in enumerate(src_points):
            # 计算相对位置
            rel_x = (src_pt[0] - min_x) / width if width > 0 else 0
            rel_y = (src_pt[1] - min_y) / height if height > 0 else 0
            
            # 映射到目标矩形 (保持相对位置)
            dst_x = rel_x * width
            dst_y = rel_y * height
            dst_points_homography.append([dst_x, dst_y])
            
        dst_points_homography = np.array(dst_points_homography, dtype=np.float32)
        
        # 使用RANSAC方法找到最佳单应性矩阵
        matrix, mask = cv2.findHomography(src_points, dst_points_homography, cv2.RANSAC, 5.0)
    
    # 执行透视变换
    transformed_image = cv2.warpPerspective(image, matrix, (int(width), int(height)))
    
    # 保存结果（如果需要）
    saved_path = None
    if save_result:
        if output_path is None:
            # 自动生成输出路径
            dir_name = os.path.dirname(image_path)
            base_name = os.path.basename(image_path)
            name, ext = os.path.splitext(base_name)
            output_path = os.path.join(dir_name, f"{name}_transformed{ext}")
        
        # 确保输出目录存在
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
        
        # 保存图像
        cv2.imwrite(output_path, transformed_image)
        saved_path = output_path
        print(f"变换后的图像已保存至: {saved_path}")
    
    return transformed_image, saved_path


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='根据坐标点文件对图片进行透视变换')
    parser.add_argument('--image_path',default='tests/frame_4045_2025_06_13_12_58_35.jpg', help='输入图像路径')
    parser.add_argument('--coords_file',default='tests/Aerobic_pool_front.txt', help='坐标点文件路径')
    parser.add_argument('--save', default=True, help='是否保存结果')
    parser.add_argument('--output',default='tests/Aerobic_pool_rear_section_output.jpg', help='输出图像路径')
    parser.add_argument('--show', default=True, help='显示结果')
    
    args = parser.parse_args()
    
    transformed_image, saved_path = transform_image_with_coords(
        args.image_path, 
        args.coords_file, 
        args.save, 
        args.output
    )
    
    if transformed_image is not None:
        if args.show:
            cv2.imshow("变换结果", transformed_image)
            cv2.waitKey(0)
            cv2.destroyAllWindows()
        
        if args.save:
            print(f"处理完成，结果保存在: {saved_path}")
        else:
            print("处理完成，未保存结果")
    else:
        print("处理失败") 