-- 创建image_metadata表的序列
DROP SEQUENCE IF EXISTS "public"."image_metadata_id_seq";
CREATE SEQUENCE "public"."image_metadata_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;
ALTER SEQUENCE "public"."image_metadata_id_seq" OWNER TO "GZe9";

-- 创建image_metadata表
DROP TABLE IF EXISTS "public"."image_metadata";
CREATE TABLE "public"."image_metadata" (
  "id" int8 NOT NULL GENERATED BY DEFAULT AS IDENTITY (
    INCREMENT 1
    MINVALUE  1
    MAXVALUE 9223372036854775807
    START 1
    CACHE 1
  ),
  "filepath" text COLLATE "pg_catalog"."default" NOT NULL,
  "capture_timestamp" timestamp(6) NOT NULL,
  "robot_state_at_capture" text COLLATE "pg_catalog"."default",
  "created_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP
);
ALTER TABLE "public"."image_metadata" OWNER TO "GZe9";

-- 添加注释
COMMENT ON COLUMN "public"."image_metadata"."id" IS '主键，自增ID';
COMMENT ON COLUMN "public"."image_metadata"."filepath" IS '图像文件的存储路径';
COMMENT ON COLUMN "public"."image_metadata"."capture_timestamp" IS '图像捕获的时间戳';
COMMENT ON COLUMN "public"."image_metadata"."robot_state_at_capture" IS '捕获时机器人的状态';
COMMENT ON COLUMN "public"."image_metadata"."created_at" IS '记录创建时间';

-- 创建索引
CREATE INDEX "idx_image_metadata_filepath" ON "public"."image_metadata" USING btree (
  "filepath" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_image_metadata_capture_timestamp" ON "public"."image_metadata" USING btree (
  "capture_timestamp" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);

-- 添加唯一约束
ALTER TABLE "public"."image_metadata" ADD CONSTRAINT "image_metadata_filepath_unique" UNIQUE ("filepath");

-- 添加主键约束
ALTER TABLE "public"."image_metadata" ADD CONSTRAINT "image_metadata_pkey" PRIMARY KEY ("id");