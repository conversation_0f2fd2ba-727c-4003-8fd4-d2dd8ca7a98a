# -------------------------------------------------------- #
#                       用于获取平台中的报警信息                      #
# -------------------------------------------------------- #

# 获取数据突变的报警
import requests
import json
import os
import sys
import yaml
from datetime import datetime, timedelta
from urllib.parse import urlencode
sys.path.append(os.getcwd())  # 添加当前工作目录到系统路径

# 加载配置文件
from config_file import config

def get_alarm_info_by_device_id(device_id=None, start_time=None, end_time=None, page_no=1, page_size=5):
    """
    获取报警信息并根据设备ID进行筛选
    
    Args:
        device_id: 设备ID，用于筛选报警信息
        start_time: 开始时间，格式为 'YYYY-MM-DD HH:MM:SS'，默认为7天前
        end_time: 结束时间，格式为 'YYYY-MM-DD HH:MM:SS'，默认为当前时间
        page_no: 页码，默认为1
        page_size: 每页数量，默认为5
    
    Returns:
        筛选后的报警信息列表
    """
    # 加载配置
    # config = load_config()
    
    # 从配置文件获取基础URL
    # base_url_domain = config.env.get("opshub_api").get("url", "http://192.168.10.110:888")
    base_url_domain = config.env.get("system").get("api").get("alarm_url")
    # 设置默认时间
    now = datetime.now()
    
    if not end_time:
        end_time = now.strftime("%Y-%m-%d %H:%M:%S")
        
    if not start_time:
        # 设置开始时间为7天前
        seven_days_ago = now - timedelta(days=7)
        start_time = seven_days_ago.strftime("%Y-%m-%d %H:%M:%S")
    
    # 构建请求参数
    params = {
        "handle_flag": 0,
        "page_no": page_no,
        "page_size": page_size,
        "alarm_code": "alarm_mutation",
        "person_id": "admin",
        "start_time": start_time,
        "end_time": end_time
    }
    
    # 构建URL
    base_url = f"{base_url_domain}/atlanta-api/alarm_info_manage/todo_api/search_list"
    # 测试
    # base_url = "http://27.188.73.44:58967/atlanta-api/alarm_info_manage/todo_api/search_list"
    url = f"{base_url}?{urlencode(params)}"
    
    try:
        # 发送GET请求
        response = requests.get(url)
        
        # 检查响应状态
        if response.status_code == 200:
            result = response.json()
            
            # 检查返回结果是否成功
            if result["code"] == 0 and "data" in result and "rows" in result["data"]:
                # 如果指定了设备ID，则进行筛选
                if device_id:
                    filtered_rows = [row for row in result["data"]["rows"] if row.get("device_id") == str(device_id)]
                    # 更新总数和行数据
                    result["data"]["rows"] = filtered_rows
                    result["data"]["total"] = len(filtered_rows)
                
                return result
            else:
                print(f"请求失败: {result.get('msg', '未知错误')}")
                return None
        else:
            print(f"请求失败，状态码: {response.status_code}")
            return None
    except Exception as e:
        print(f"请求异常: {str(e)}")
        return None


if __name__ == "__main__":
    result = get_alarm_info_by_device_id(device_id=314)
    print(result)