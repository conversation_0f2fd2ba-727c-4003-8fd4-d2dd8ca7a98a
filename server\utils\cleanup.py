import logging
from datetime import datetime, timedelta
from pathlib import Path
import shutil

from server.task.service import cleanup_old_frame_analysis_data_smart

"""
数据清理管理模块

主要功能：
1. 清理过期的图片文件
2. 清理数据库中的历史数据
3. 智能管理存储空间
"""

class DataCleanupManager:
    """数据清理管理器类
    
    负责管理和执行数据清理任务，包括图片文件和数据库记录
    
    Attributes:
        base_dataset_path (Path): 数据集基础路径
        image_retention_days (int): 图片保留天数
        cleanup_months (int): 数据库记录保留月数
        cleanup_day (int): 每月执行清理的日期
    """
    
    def __init__(self, config: dict):
        """初始化数据清理管理器
        
        Args:
            config (dict): 配置字典，包含：
                - storage.paths.base_dataset: 数据集基础路径
                - cleanup.image_retention_days: 图片保留天数
                - cleanup.cleanup_months: 数据库记录保留月数
                - cleanup.cleanup_day: 每月执行清理的日期
        """
        self.base_dataset_path = Path(config['storage']['paths']['base_dataset'])
        self.image_retention_days = config['cleanup']['image_retention_days']
        self.cleanup_months = config['cleanup']['cleanup_months']
        self.cleanup_day = config['cleanup']['cleanup_day']

    def cleanup_old_images(self):
        """清理超过保留期限的图片和相关数据
        
        执行流程：
        1. 计算保留期限日期
        2. 清理数据库中的旧数据
        3. 清理文件系统中的旧图片
        
        异常处理：
        - 记录所有清理过程中的错误
        - 继续执行即使部分清理失败
        """
        try:
            # 计算保留期限的日期
            retention_date = datetime.now() - timedelta(days=self.image_retention_days)
            
            # 清理数据库中的旧数据
            cleanup_old_frame_analysis_data_smart(
                months=self.cleanup_months, 
                cleanup_day=self.cleanup_day
            )
            logging.info("完成清理数据库中的过期数据")
            
            # 清理文件系统中的旧图片
            self._cleanup_old_image_files(retention_date)
            
            logging.info("完成清理过期图片")
            
        except Exception as e:
            logging.error(f"清理过期数据时发生错误: {str(e)}")

    def _cleanup_old_image_files(self, retention_date: datetime):
        """清理文件系统中的旧图片
        
        按照年/月/日的目录结构清理过期图片文件
        
        Args:
            retention_date (datetime): 保留期限的日期
            
        清理策略：
        1. 遍历年份目录
        2. 遍历月份目录
        3. 遍历日期目录
        4. 删除过期的日期目录
        5. 清理空的月份和年份目录
        
        安全特性：
        - 仅删除指定目录结构下的文件
        - 保留目录结构的完整性
        - 记录所有删除操作
        """
        for year_dir in self.base_dataset_path.glob('*'):
            if not year_dir.is_dir():
                continue
                
            for month_dir in year_dir.glob('*'):
                if not month_dir.is_dir():
                    continue
                    
                for day_dir in month_dir.glob('*'):
                    if not day_dir.is_dir():
                        continue
                        
                    try:
                        # 解析目录日期
                        dir_date = datetime.strptime(
                            f"{year_dir.name}/{month_dir.name}/{day_dir.name}", 
                            "%Y/%m/%d"
                        )
                        
                        # 如果目录日期早于保留期限
                        if dir_date < retention_date:
                            logging.info(f"删除过期目录: {day_dir}")
                            shutil.rmtree(day_dir)
                            
                            # 如果月份目录为空，删除月份目录
                            if not any(month_dir.iterdir()):
                                month_dir.rmdir()
                                
                            # 如果年份目录为空，删除年份目录
                            if not any(year_dir.iterdir()):
                                year_dir.rmdir()
                                
                    except ValueError as e:
                        logging.error(f"解析目录日期时出错: {e}")
                        continue 