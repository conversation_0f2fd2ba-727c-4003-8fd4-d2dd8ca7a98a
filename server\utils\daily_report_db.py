"""
设备日报表数据库操作工具

提供对设备日报表(device_daily_report)的数据库操作
"""
import json
from datetime import datetime
import sys
import os
import logging

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from server.utils import pg_tool
from server.utils.report_templates import generate_device_report, generate_daily_report
from server.utils.logger import setup_logging

# 配置日志
setup_logging()
logger = logging.getLogger(__name__)

def save_daily_report(camera_id, report_date=None, submitter="AI巡检员"):
    """
    生成并保存设备日报到数据库
    
    Args:
        camera_id (str): 设备ID/摄像头ID
        report_date (str, optional): 报告日期，格式为'YYYY-MM-DD'，默认为当前日期
        submitter (str, optional): 提交人，默认为"AI巡检员"
        
    Returns:
        int: 插入或更新的记录ID，失败返回None
        
    Raises:
        Exception: 数据库操作异常
    """
    # 如果没有提供报告日期，使用当前日期
    if report_date is None:
        report_date = datetime.now().strftime('%Y-%m-%d')
    
    try:
        # 生成设备报告
        # device_report = generate_device_report(camera_id, report_date)
        device_name, normal_count, warning_count, transition_count, normal_analysis, warning_analysis, transition_analysis, full_report_text = generate_daily_report(camera_id, report_date)
        
        # 连接数据库
        conn = pg_tool.connect_db()
        if not conn:
            logger.error("数据库连接失败")
            return None
        
        # 准备数据
        # device_name = device_report['device_info']['device_name']
        # normal_count = device_report['summary']['normal_count']
        # warning_count = device_report['summary']['warning_count']
        # transition_count = device_report['summary']['transition_count']
        # normal_analysis = device_report['analysis']['normal_analysis']
        # warning_analysis = device_report['analysis']['warning_analysis']
        # transition_analysis = device_report['analysis']['transition_analysis']
        
        # 将原始数据转换为JSON字符串
        # raw_data_json = json.dumps({
        #     'warning_data': device_report['raw_data']['warning_data'],
        #     'non_warning_data': device_report['raw_data']['non_warning_data'],
        #     'transition_data': device_report['raw_data']['transition_data']
        # })
        raw_data_json = None
        try:
            cur = conn.cursor()
            
            # 检查是否已存在该设备当天的报告
            check_sql = """
                SELECT id FROM device_daily_report
                WHERE camera_id = %s AND report_date = %s
            """
            cur.execute(check_sql, (camera_id, report_date))
            existing_record = cur.fetchone()
            
            if existing_record:
                # 如果已存在，进行更新
                update_sql = """
                    UPDATE device_daily_report
                    SET device_name = %s,
                        normal_count = %s,
                        warning_count = %s,
                        transition_count = %s,
                        normal_analysis = %s,
                        warning_analysis = %s,
                        transition_analysis = %s,
                        full_report = %s,
                        raw_data = %s,
                        submitter = %s,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = %s
                    RETURNING id
                """
                cur.execute(update_sql, (
                    device_name, normal_count, warning_count, transition_count,
                    normal_analysis, warning_analysis, transition_analysis,
                    full_report_text, raw_data_json, submitter, existing_record[0]
                ))
                result = cur.fetchone()
                record_id = result[0] if result else None
                logger.info(f"更新设备 {camera_id} 的日报 ({report_date})，ID: {record_id}")
            else:
                # 如果不存在，插入新记录
                insert_sql = """
                    INSERT INTO device_daily_report (
                        camera_id, device_name, report_date,
                        normal_count, warning_count, transition_count,
                        normal_analysis, warning_analysis, transition_analysis,
                        full_report, raw_data, submitter
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    RETURNING id
                """
                cur.execute(insert_sql, (
                    camera_id, device_name, report_date,
                    normal_count, warning_count, transition_count,
                    normal_analysis, warning_analysis, transition_analysis,
                    full_report_text, raw_data_json, submitter
                ))
                result = cur.fetchone()
                record_id = result[0] if result else None
                logger.info(f"插入设备 {camera_id} 的日报 ({report_date})，ID: {record_id}")
            
            conn.commit()
            return record_id
            
        except Exception as e:
            conn.rollback()
            logger.error(f"保存日报数据失败: {e}")
            return None
        finally:
            if cur:
                cur.close()
            if conn:
                conn.close()

    except Exception as e:
        logger.error(f"生成或保存日报失败: {e}")
        return None


def get_daily_report(camera_id, report_date=None):
    """
    获取设备日报
    
    Args:
        camera_id (str): 设备ID/摄像头ID
        report_date (str, optional): 报告日期，格式为'YYYY-MM-DD'，默认为当前日期
        
    Returns:
        dict: 设备日报数据，失败返回None
        
    Raises:
        Exception: 数据库查询异常
    """
    # 如果没有提供报告日期，使用当前日期
    if report_date is None:
        report_date = datetime.now().strftime('%Y-%m-%d')
    
    conn = pg_tool.connect_db()
    if not conn:
        logger.error("数据库连接失败")
        return None
    
    try:
        cur = conn.cursor()
        
        # 查询日报数据
        query_sql = """
            SELECT 
                id, camera_id, device_name, report_date,
                normal_count, warning_count, transition_count,
                normal_analysis, warning_analysis, transition_analysis,
                full_report, raw_data, created_at, updated_at, submitter,
                (EXTRACT(YEAR FROM report_date)::text || '年' || 
                 EXTRACT(MONTH FROM report_date)::text || '月' || 
                 EXTRACT(DAY FROM report_date)::text || '日' || 
                 device_name || '巡检日报') AS report_name
            FROM device_daily_report
            WHERE camera_id = %s AND report_date = %s
        """
        
        cur.execute(query_sql, (camera_id, report_date))
        result = cur.fetchone()
        
        if not result:
            logger.warning(f"未找到设备 {camera_id} 的日报 ({report_date})")
            return None
        
        # 构建返回数据
        fields = [
            "id", "camera_id", "device_name", "report_date",
            "normal_count", "warning_count", "transition_count",
            "normal_analysis", "warning_analysis", "transition_analysis",
            "full_report", "raw_data", "created_at", "updated_at", "submitter",
            "report_name"
        ]
        
        report_data = dict(zip(fields, result))
        
        # 如果raw_data是JSON字符串，转换为字典
        if isinstance(report_data.get('raw_data'), str):
            try:
                report_data['raw_data'] = json.loads(report_data['raw_data'])
            except json.JSONDecodeError:
                logger.warning(f"无法解析raw_data JSON: {report_data['raw_data']}")

        return report_data

    except Exception as e:
        logger.error(f"查询日报数据失败: {e}")
        return None
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()


def list_daily_reports(start_date=None, end_date=None, camera_id=None, limit=100, offset=0):
    """
    列出设备日报
    
    Args:
        start_date (str, optional): 开始日期，格式为'YYYY-MM-DD'
        end_date (str, optional): 结束日期，格式为'YYYY-MM-DD'
        camera_id (str, optional): 设备ID/摄像头ID
        limit (int, optional): 限制返回记录数，默认100
        offset (int, optional): 偏移量，用于分页，默认0
        
    Returns:
        list: 设备日报列表
        
    Raises:
        Exception: 数据库查询异常
    """
    conn = pg_tool.connect_db()
    if not conn:
        logger.error("数据库连接失败")
        return []
    
    try:
        cur = conn.cursor()
        
        # 构建查询SQL
        query_sql = """
            SELECT 
                id, camera_id, device_name, report_date,
                normal_count, warning_count, transition_count,
                created_at, updated_at, submitter,
                (EXTRACT(YEAR FROM report_date)::text || '年' || 
                 EXTRACT(MONTH FROM report_date)::text || '月' || 
                 EXTRACT(DAY FROM report_date)::text || '日' || 
                 device_name || '巡检日报') AS report_name
            FROM device_daily_report
            WHERE 1=1
        """
        
        params = []
        
        # 添加查询条件
        if camera_id:
            query_sql += " AND camera_id = %s"
            params.append(camera_id)
        
        if start_date:
            query_sql += " AND report_date >= %s"
            params.append(start_date)
        
        if end_date:
            query_sql += " AND report_date <= %s"
            params.append(end_date)
        
        # 添加排序、分页
        query_sql += " ORDER BY report_date DESC, camera_id"
        query_sql += " LIMIT %s OFFSET %s"
        params.extend([limit, offset])
        
        # 执行查询
        cur.execute(query_sql, params)
        results = cur.fetchall()
        
        # 构建返回数据
        fields = [
            "id", "camera_id", "device_name", "report_date",
            "normal_count", "warning_count", "transition_count",
            "created_at", "updated_at", "submitter", "report_name"
        ]
        
        reports_list = []
        for row in results:
            report_data = dict(zip(fields, row))
            reports_list.append(report_data)
        
        return reports_list

    except Exception as e:
        logger.error(f"列出日报数据失败: {e}")
        return []
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()


def delete_daily_report(report_id):
    """
    删除设备日报
    
    Args:
        report_id (int): 日报ID
        
    Returns:
        bool: 删除是否成功
        
    Raises:
        Exception: 数据库操作异常
    """
    conn = pg_tool.connect_db()
    if not conn:
        logger.error("数据库连接失败")
        return False
    
    try:
        cur = conn.cursor()
        
        # 删除日报
        delete_sql = """
            DELETE FROM device_daily_report
            WHERE id = %s
        """
        
        cur.execute(delete_sql, (report_id,))
        conn.commit()
        
        deleted_rows = cur.rowcount
        success = deleted_rows > 0
        
        if success:
            logger.info(f"成功删除日报，ID: {report_id}")
        else:
            logger.warning(f"未找到ID为 {report_id} 的日报")

        return success

    except Exception as e:
        conn.rollback()
        logger.error(f"删除日报失败: {e}")
        return False
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()


if __name__ == "__main__":
    # 测试生成并保存日报
    camera_id = "4052"
    current_date = datetime.now().strftime('%Y-%m-%d')

    logger.info(f"为设备 {camera_id} 生成 {current_date} 的日报")
    report_id = save_daily_report(camera_id, current_date)

    if report_id:
        logger.info(f"日报已保存，ID: {report_id}")

        # 测试获取日报
        logger.info(f"获取设备 {camera_id} 的日报 ({current_date})")
        report_data = get_daily_report(camera_id, current_date)

        if report_data:
            logger.info(f"日报ID: {report_data['id']}")
            logger.info(f"设备名称: {report_data['device_name']}")
            logger.info(f"正常记录数: {report_data['normal_count']}")
            logger.info(f"告警记录数: {report_data['warning_count']}")
            logger.info(f"状态转变记录数: {report_data['transition_count']}")
            logger.info(f"创建时间: {report_data['created_at']}")
            logger.info(f"更新时间: {report_data['updated_at']}")

        # 测试列出日报
        logger.info("列出最近的日报")
        reports = list_daily_reports(limit=5)

        for idx, report in enumerate(reports):
            logger.info(f"日报 {idx+1}:")
            logger.info(f"ID: {report['id']}")
            logger.info(f"设备ID: {report['camera_id']}")
            logger.info(f"设备名称: {report['device_name']}")
            logger.info(f"报告日期: {report['report_date']}")
    else:
        logger.error("日报生成失败")
