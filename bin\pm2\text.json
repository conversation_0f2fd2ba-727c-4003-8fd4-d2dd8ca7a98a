{"apps": [{"name": "textrun", "script": "/bin/bash", "args": ["-c", "CUDA_VISIBLE_DEVICES=0 swift deploy --model_type qwen2_5-7b-instruct-awq --infer_backend vllm --max_model_len 4096 --model_id_or_path /home/<USER>/ai_project/opshub-ai/llms/models/textmodel/data/ --log_interval 0 --port 8000 --host '0.0.0.0'"], "env": {"CUDA_VISIBLE_DEVICES": "0"}, "cwd": "/home/<USER>/ai_project/opshub-ai", "error_file": "/home/<USER>/ai_project/opshub-ai/llms/models/textmodel/textrun-error.log", "out_file": "/home/<USER>/ai_project/opshub-ai/llms/models/textmodel/textrun-out.log", "merge_logs": true}]}