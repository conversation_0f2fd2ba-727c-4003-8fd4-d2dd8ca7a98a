# # 使用Python作为基础镜像
# FROM python:3.10.15
# # 设置工作目录
# WORKDIR /model
# WORKDIR /app
# # 复制应用代码到容器中
# COPY /llms/models/visionmodel/requirements.txt /app
# # pip镜像源
# RUN pip3 config set global.index-url http://mirrors.aliyun.com/pypi/simple
# RUN pip3 config set install.trusted-host mirrors.aliyun.com
# # 安装依赖项
# RUN pip install --no-cache-dir -r requirements.txt
# # 暴露应用端口
# EXPOSE 8001
# #定义时区参数
# ENV TZ=Asia/Shanghai
# #设置时区
# RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo '$TZ' > /etc/timezone
# #设置编码
# ENV LANG C.UTF-8
# # 设置启动命令
# CMD CUDA_VISIBLE_DEVICES=1 swift deploy --model_type qwen2-vl-7b-instruct-awq --infer_backend vllm  --model_id_or_path /model --log_interval 0 --port 8001 --host '127.0.0.1' --limit_mm_per_prompt '{"image": 2}'