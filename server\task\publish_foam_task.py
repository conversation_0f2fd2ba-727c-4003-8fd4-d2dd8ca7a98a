"""
定时发布泡沫数据的任务模块
每隔20秒从数据库获取最新的泡沫覆盖率数据并通过MQTT发布
"""
import sys
import os
# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
import logging
import threading
import time
from typing import Dict
from datetime import datetime

from server.utils.mqtt_tool import publish_foam_data
from server.utils.get_cameras_info import get_cameras_config
from server.utils.data_publisher import DataPublisher
from server.utils.logger import setup_logging
from config_file import config

class FoamDataPublisher:
    """泡沫数据发布器
    
    负责定时获取泡沫覆盖率数据并通过MQTT发布
    """
    
    def __init__(self):
        """初始化泡沫数据发布器"""
        self.config = config.env
        self.system_running = True
        self.data_publisher = DataPublisher(self.config)
        self.publish_interval = 20  # 发布间隔，单位秒
        self.publisher_thread = None
        
    def start(self):
        """启动泡沫数据发布任务"""
        if self.publisher_thread is None or not self.publisher_thread.is_alive():
            self.system_running = True
            self.publisher_thread = threading.Thread(
                target=self._publish_loop,
                name="FoamPublisher",
                daemon=True
            )
            self.publisher_thread.start()
            logging.info(f"泡沫数据发布任务已启动，发布间隔: {self.publish_interval}秒")
        else:
            logging.warning("泡沫数据发布任务已在运行中")
            
    def stop(self):
        """停止泡沫数据发布任务"""
        self.system_running = False
        if self.publisher_thread and self.publisher_thread.is_alive():
            self.publisher_thread.join(timeout=5)
            logging.info("泡沫数据发布任务已停止")
    
    def _publish_loop(self):
        """发布循环，定时获取和发布泡沫数据"""
        logging.info("开始泡沫数据发布循环")
        
        while self.system_running:
            try:
                self._publish_all_foam_data()
                time.sleep(self.publish_interval)
            except Exception as e:
                logging.error(f"泡沫数据发布过程中发生错误: {str(e)}")
                time.sleep(5)  # 出错后稍微等待一会再重试
    
    def _publish_all_foam_data(self):
        """获取并发布所有摄像头的泡沫数据"""
        try:
            # 获取摄像头配置
            cameras_config = get_cameras_config()
            if not cameras_config:
                logging.warning("获取摄像头配置失败，跳过本次发布")
                return
            
            published_count = 0
            for camera_config in cameras_config:
                try:
                    camera_id = camera_config['camera_id']
                    video_id = camera_config['video_id']
                    
                    # 获取覆盖率数据
                    coverage_rate = self.data_publisher.get_coverage_rate(camera_id)
                    
                    # 如果有有效数据，则发布
                    if coverage_rate != '-' and coverage_rate != 1 and coverage_rate != 99 :
                        publish_foam_data(video_id, coverage_rate)
                        published_count += 1
                        logging.info(f"已发布 - 摄像头: {camera_id}, 设备: {video_id}, 覆盖率: {coverage_rate}")
                except Exception as e:
                    logging.error(f"处理摄像头 {camera_id} 的泡沫数据时发生错误: {str(e)}")
                    continue
            
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            logging.info(f"{timestamp} - 本次发布完成，成功发布 {published_count}/{len(cameras_config)} 个设备的泡沫数据")
            
        except Exception as e:
            logging.error(f"发布泡沫数据时发生错误: {str(e)}")

# 全局发布器实例
foam_publisher = None

def start():
    """启动泡沫数据发布任务"""
    global foam_publisher
    try:
        # 设置日志
        setup_logging()
        
        # 创建并启动发布器
        foam_publisher = FoamDataPublisher()
        foam_publisher.start()
        
        # 保持主线程运行
        while True:
            time.sleep(60)
    except KeyboardInterrupt:
        logging.info("收到停止信号，正在关闭发布任务...")
        if foam_publisher:
            foam_publisher.stop()
    except Exception as e:
        logging.error(f"发布任务运行时发生错误: {str(e)}")
        if foam_publisher:
            foam_publisher.stop()

if __name__ == "__main__":
    start()
