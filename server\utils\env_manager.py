from pathlib import Path
from dotenv import load_dotenv
import os
import logging
from config_file import config
"""
环境变量管理模块

主要功能：
1. 加载和验证环境变量
2. 确保必要的API密钥和配置存在
3. 提供环境变量加载的错误处理
"""


class EnvManager:
    """环境变量管理器类
    
    负责加载和验证项目所需的环境变量
    """

    @staticmethod
    def load_env():
        """加载环境变量
        
        从项目根目录的configs/.env文件加载环境变量，并验证必要变量的存在
        
        必要的环境变量包括：
        - GLM_API_KEY: 用于GLM API的密钥
        - GLM_BASE_URL: GLM API的基础URL
        - GLM_MODEL: GLM使用的模型名称
        - QWEN2_VL_API_KEY: 用于Qwen2-VL API的密钥
        - QWEN2_VL_BASE_URL: Qwen2-VL API的基础URL
        - QWEN2_VL_MODEL: Qwen2-VL使用的模型名称
        
        Raises:
            FileNotFoundError: 当环境变量文件不存在时
            EnvironmentError: 当无法加载环境变量文件或缺少必要的环境变量时
            
        验证流程：
        1. 检查环境变量文件是否存在
        2. 加载环境变量文件
        3. 验证所有必要的环境变量是否存在
        4. 记录加载成功的日志
        """
        # 获取项目根目录
        project_root = Path(__file__).parent.parent.parent
        env_path = project_root / config.args.env_file

        # 检查环境变量文件是否存在
        if not env_path.exists():
            raise FileNotFoundError(f"环境变量文件不存在: {env_path}")

        # 加载环境变量
        if not load_dotenv(env_path):
            raise EnvironmentError("无法加载环境变量文件")

        # 验证必要的环境变量
        required_vars = {
            'GLM_API_KEY': '用于GLM API的密钥',
            'GLM_BASE_URL': 'GLM API的基础URL',
            'GLM_MODEL': 'GLM使用的模型名称',
            'QWEN2_VL_API_KEY': '用于Qwen2-VL API的密钥',
            'QWEN2_VL_BASE_URL': 'Qwen2-VL API的基础URL',
            'QWEN2_VL_MODEL': 'Qwen2-VL使用的模型名称'
        }

        missing_vars = []
        for var, description in required_vars.items():
            if not os.getenv(var):
                missing_vars.append(f"{var} ({description})")

        if missing_vars:
            raise EnvironmentError(
                "缺少必要的环境变量:\n" +
                "\n".join(f"- {var}" for var in missing_vars)
            )

        logging.info("环境变量加载成功")
