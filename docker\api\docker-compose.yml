# version: "3.3"

services:

  opshub-ai:
    container_name: opshub-ai-new_test
    image: opshub-ai-new:v1.0.0  # 自定义镜像名称和版本标签
    build:
      context: ../../  # 切换上下文起始地址
      dockerfile: docker/api/Dockerfile  #指定dockerfile
    stdin_open: true
    tty: true
    ports:
      - '8102:8102'
    # 启动前显示容器时间，然后启动应用
    command: bash -c "date && python app_run.py"
    volumes:
      # 挂载项目文件（包含所有代码和配置文件）
      - /home/<USER>/llm_project/datansha/development/opshub-ai:/app/

      # 将datasets和logs目录挂载到与代码中使用的路径一致的位置
      - /home/<USER>/llm_project/datansha/save_data/datasets:/home/<USER>/llm_project/datansha/save_data/datasets
      - /home/<USER>/llm_project/datansha/development/opshub-ai/logs:/home/<USER>/llm_project/datansha/development/opshub-ai/logs

      # 数据输出目录
      # - /ssd/LLM/video_ai/datamodel/development/new/data:/app/data

      # 同步主机时间
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro
    # 设置工作目录
    working_dir: /app
    environment:
      # 设置时区
      - TZ=Asia/Shanghai
      # 设置编码
      - LANG=C.UTF-8
      # pip镜像源配置（如果需要在容器内安装其他包）
      - PIP_INDEX_URL=http://mirrors.aliyun.com/pypi/simple
      - PIP_TRUSTED_HOST=mirrors.aliyun.com
    logging:
      driver: "json-file"
      options:
        max-size: "20m"
        max-file: "30"
    networks:
      - my-network

networks:
  my-network:
    # 自动创建网络，而不是使用外部网络
    name: my-network