import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import cv2
import base64
import numpy as np
import logging
from llms.config import SYSTEM
from llms.config_filter import SYSTEM_FILTER
from llms.config_aerobic import SYSTEM_AEROBIC
from llms.config_sludge_discharge import SYSTEM_SLUDGE_DISCHARGE
from openai import OpenAI
import os
from datetime import datetime
from http.server import BaseHTTPRequestHandler, HTTPServer
import json
from config_file import config  # 导入全局配置实例

# # 设置日志路径
# current_datetime = datetime.now()
# log_directory = f"logs/{current_datetime.year}/{current_datetime.month:02d}/{current_datetime.day:02d}/"
# os.makedirs(log_directory, exist_ok=True)
# log_filename = f"{log_directory}{current_datetime.strftime('%Y%m%d_%H%M%S')}.log"

# # 设置日志配置
# logging.basicConfig(filename=log_filename, level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
# system_prompt = SYSTEM['suggestion_prompt1']
MAX_RETRIES = 3

def llm_process_image_filter(filter_result: str, system_type: str) -> str:

    # 使用配置实例中的环境变量
    client = OpenAI(
        api_key=config.env_vars.get("GLM_API_KEY"),
        base_url=config.env_vars.get("GLM_BASE_URL")
    )
    model = config.env_vars.get("GLM_MODEL")
    
    query = "根据提示词分析图片并输出内容。"
    # system_prompt = SYSTEM['suggestion_prompt1']
    print(f"---------------------------")
    print("滤池文本系统提示词是：: system_prompt_filter1 or system_prompt_filter_multiple")
    print(f"---------------------------")
    if system_type == 'system_prompt_filter1' or system_type=='system_prompt_filter_multiple':
        system_prompt = SYSTEM_FILTER['suggestion_prompt_filter1']

    # else:
    #     system_prompt = SYSTEM['suggestion_prompt1']
    messages = [
        {'role': 'system', 'content': system_prompt},
        {
        'role': 'user',
            'content': f"图片视觉分析出的内容:{filter_result}",
        }
    ]
    
    for retry in range(MAX_RETRIES):
        try:
            resp = client.chat.completions.create(
                model=model,
                messages=messages,
                seed=42
            )
            response_text = resp.choices[0].message.content
            logging.info(f"获得响应: {response_text}")
            return response_text
        except Exception as e:
            logging.error(f"请求失败, 尝试重新请求 ({retry+1}/{MAX_RETRIES}): {e}")

    logging.error("请求失败次数过多,已达到最大重试次数,请检查网络或API密钥")
    return None

def evaluate_parameters(DO: float, MLSS: float, system_type: str) -> str:
    """
    根据系统类型评估DO和MLSS参数是否在理想范围内
    
    Args:
        DO (float): 溶解氧浓度，单位mg/L
        MLSS (float): 混合液悬浮固体浓度，单位mg/L
        system_type (str): 系统提示词类型
    
    Returns:
        str: 参数评估结果描述
    """
    results = []

    # 从配置文件中获取参数范围
    if system_type == 'system_prompt_aerobic_single1' or system_type == 'system_prompt_aerobic_multiple1':
        # 从配置文件获取system_prompt_aerobic1的参数范围
        param_config = config.env.get('parameter_ranges', {}).get('aerobic', {}).get('system_prompt_aerobic1', {})
        
        # 获取DO参数
        do_params = param_config.get('do', {})
        do_ideal_min = do_params.get('ideal_min', 0.2)
        do_ideal_max = do_params.get('ideal_max', 1.3)
        do_min_limit = do_params.get('min_limit', 0.04)
        do_max_limit = do_params.get('max_limit', 4.95)
        
        # 获取MLSS参数，但不会用于评估
        mlss_params = param_config.get('mlss', {})
        mlss_ideal_min = mlss_params.get('ideal_min', 3000)
        mlss_ideal_max = mlss_params.get('ideal_max', 4500)
        mlss_min_limit = mlss_params.get('min_limit', 1000)
        mlss_max_limit = mlss_params.get('max_limit', 5000)
        # 仅添加DO参数范围概述
        range_summary = f"DO理想范围: {do_ideal_min}-{do_ideal_max} mg/L, 允许范围: {do_min_limit}-{do_max_limit} mg/L"
        results.append(range_summary)
    elif system_type == 'system_prompt_aerobic_single2' or system_type == 'system_prompt_aerobic_multiple2':
        # 从配置文件获取system_prompt_aerobic2的参数范围
        param_config = config.env.get('parameter_ranges', {}).get('aerobic', {}).get('system_prompt_aerobic2', {})
        
        # 获取DO参数
        do_params = param_config.get('do', {})
        do_ideal_min = do_params.get('ideal_min', 1.5)
        do_ideal_max = do_params.get('ideal_max', 3.5)
        do_min_limit = do_params.get('min_limit', 0.86)
        do_max_limit = do_params.get('max_limit', 4.0)
        
        # 获取MLSS参数
        mlss_params = param_config.get('mlss', {})
        mlss_ideal_min = mlss_params.get('ideal_min', 3000)
        mlss_ideal_max = mlss_params.get('ideal_max', 4500)
        mlss_min_limit = mlss_params.get('min_limit', 1000)
        mlss_max_limit = mlss_params.get('max_limit', 5000)
        # 添加参数范围概述
        range_summary = f"DO理想范围: {do_ideal_min}-{do_ideal_max} mg/L, 允许范围: {do_min_limit}-{do_max_limit} mg/L; MLSS理想范围: {mlss_ideal_min}-{mlss_ideal_max} mg/L, 允许范围: {mlss_min_limit}-{mlss_max_limit} mg/L"
        results.append(range_summary)
    else:
        # 默认使用system_prompt_aerobic1的参数范围
        param_config = config.env.get('parameter_ranges', {}).get('aerobic', {}).get('system_prompt_aerobic1', {})
        
        # 获取DO参数
        do_params = param_config.get('do', {})
        do_ideal_min = do_params.get('ideal_min', 0.2)
        do_ideal_max = do_params.get('ideal_max', 1.3)
        do_min_limit = do_params.get('min_limit', 0.04)
        do_max_limit = do_params.get('max_limit', 4.95)
        
        # 获取MLSS参数
        mlss_params = param_config.get('mlss', {})
        mlss_ideal_min = mlss_params.get('ideal_min', 3000)
        mlss_ideal_max = mlss_params.get('ideal_max', 4500)
        mlss_min_limit = mlss_params.get('min_limit', 1000)
        mlss_max_limit = mlss_params.get('max_limit', 5000)
    
        # 添加参数范围概述
        range_summary = f"DO理想范围: {do_ideal_min}-{do_ideal_max} mg/L, 允许范围: {do_min_limit}-{do_max_limit} mg/L; MLSS理想范围: {mlss_ideal_min}-{mlss_ideal_max} mg/L, 允许范围: {mlss_min_limit}-{mlss_max_limit} mg/L"
        results.append(range_summary)
    
    # DO评估
    if DO < do_min_limit:
        do_increase = round(do_ideal_min - DO, 2)
        target_range = f"{do_ideal_min}-{do_ideal_max}"
        results.append(f"溶解氧(DO)浓度为{DO} mg/L，严重低于最低限值({do_min_limit} mg/L)，可能导致厌氧条件形成，需要增加约{do_increase} mg/L的DO才能达到最低理想值{do_ideal_min} mg/L，理想范围为{target_range} mg/L")
    elif DO < do_ideal_min:
        do_increase = round(do_ideal_min - DO, 2)
        target_range = f"{do_ideal_min}-{do_ideal_max}"
        results.append(f"溶解氧(DO)浓度为{DO} mg/L，低于理想范围({do_ideal_min}-{do_ideal_max} mg/L)，需要增加曝气量，建议增加约{do_increase} mg/L的DO才能达到理想范围下限{do_ideal_min} mg/L，理想范围为{target_range} mg/L")
    elif do_ideal_min <= DO <= do_ideal_max:
        results.append(f"溶解氧(DO)浓度为{DO} mg/L，在理想范围内({do_ideal_min}-{do_ideal_max} mg/L)")
    elif DO <= do_max_limit:
        do_decrease = round(DO - do_ideal_max, 2)
        target_range = f"{do_ideal_min}-{do_ideal_max}"
        results.append(f"溶解氧(DO)浓度为{DO} mg/L，高于理想范围但未超过最高限值({do_max_limit} mg/L)，可适当减少曝气量，建议减少约{do_decrease} mg/L的DO以回到理想范围上限{do_ideal_max} mg/L，理想范围为{target_range} mg/L")
    else:
        do_decrease = round(DO - do_max_limit, 2)
        target_range = f"{do_ideal_min}-{do_ideal_max}"
        results.append(f"溶解氧(DO)浓度为{DO} mg/L，超过最高限值({do_max_limit} mg/L)，需要立即减少曝气量，建议至少减少{do_decrease} mg/L的DO以降至安全水平{do_max_limit} mg/L，理想范围为{target_range} mg/L")
    
    # MLSS评估 - 仅在非system_prompt_aerobic_single1和非system_prompt_aerobic_multiple1时执行
    if system_type != 'system_prompt_aerobic_single1' and system_type != 'system_prompt_aerobic_multiple1':
        if MLSS < mlss_min_limit:
            mlss_increase = round(mlss_min_limit - MLSS)
            target_range = f"{mlss_ideal_min}-{mlss_ideal_max}"
            results.append(f"混合液悬浮固体浓度(MLSS)为{MLSS} mg/L，低于最低限值({mlss_min_limit} mg/L)，污泥量不足，需要增加约{mlss_increase} mg/L的MLSS才能达到最低限值{mlss_min_limit} mg/L，理想范围为{target_range} mg/L")
        elif MLSS < mlss_ideal_min:
            mlss_increase = round(mlss_ideal_min - MLSS)
            target_range = f"{mlss_ideal_min}-{mlss_ideal_max}"
            results.append(f"混合液悬浮固体浓度(MLSS)为{MLSS} mg/L，低于理想范围({mlss_ideal_min}-{mlss_ideal_max} mg/L)，需要减少排泥量，建议增加约{mlss_increase} mg/L的MLSS才能达到理想范围下限{mlss_ideal_min} mg/L，理想范围为{target_range} mg/L")
        elif mlss_ideal_min <= MLSS <= mlss_ideal_max:
            results.append(f"混合液悬浮固体浓度(MLSS)为{MLSS} mg/L，在理想范围内({mlss_ideal_min}-{mlss_ideal_max} mg/L)")
        elif MLSS <= mlss_max_limit:
            mlss_decrease = round(MLSS - mlss_ideal_max)
            target_range = f"{mlss_ideal_min}-{mlss_ideal_max}"
            results.append(f"混合液悬浮固体浓度(MLSS)为{MLSS} mg/L，高于理想范围但未超过最高限值({mlss_max_limit} mg/L)，需要适当增加排泥量，建议减少约{mlss_decrease} mg/L的MLSS以回到理想范围上限{mlss_ideal_max} mg/L，理想范围为{target_range} mg/L")
        else:
            mlss_decrease = round(MLSS - mlss_max_limit)
            target_range = f"{mlss_ideal_min}-{mlss_ideal_max}"
            results.append(f"混合液悬浮固体浓度(MLSS)为{MLSS} mg/L，超过最高限值({mlss_max_limit} mg/L)，需要立即增加排泥量，建议至少减少{mlss_decrease} mg/L的MLSS以降至安全水平{mlss_max_limit} mg/L，理想范围为{target_range} mg/L")
    
    return "；".join(results)

def llm_process_image_aerobic(filter_result: str, DO: float, MLSS: float, bubble_area: float, system_type: str) -> str:
    """
    处理图像数据并通过LLM模型生成分析结果
    
    Args:
        DO (float): 溶解氧浓度，单位mg/L
        MLSS (float): 混合液悬浮固体浓度，单位mg/L
        bubble_area (float): 泡沫覆盖率，单位%
    
    Returns:
        str: LLM模型的分析响应文本，如果处理失败则返回None
    """
    # # 测试DO和MLSS
    # DO = 2.5
    # MLSS = 2210
    # 使用配置实例中的环境变量
    client = OpenAI(
        api_key=config.env_vars.get("GLM_API_KEY"),
        base_url=config.env_vars.get("GLM_BASE_URL")
    )
    model = config.env_vars.get("GLM_MODEL")
    
    query = "根据提示词分析图片并输出内容。"

    if system_type == 'system_prompt_aerobic_single1' or system_type == 'system_prompt_aerobic_multiple1':
        system_prompt = SYSTEM_AEROBIC['suggestion_prompt_aerobic1']
        print(f"---------------------------")
        print("好氧池文本系统提示词是：: suggestion_prompt_aerobic1")
        print(f"---------------------------")
    elif system_type == 'system_prompt_aerobic_single2' or system_type == 'system_prompt_aerobic_multiple2':
        system_prompt = SYSTEM_AEROBIC['suggestion_prompt_aerobic2']
        print(f"---------------------------")
        print("好氧池文本系统提示词是：: suggestion_prompt_aerobic2")
        print(f"---------------------------")
    else:
        system_prompt = SYSTEM['suggestion_prompt1']
    
    # 评估DO和MLSS参数，传递system_type
    parameters_evaluation = evaluate_parameters(DO, MLSS, system_type)
    
    messages = [
        {'role': 'system', 'content': system_prompt},
        {
        'role': 'user',
            'content': f"当前泡沫覆盖率:{bubble_area}%, DO数值:{DO}mg/L, MLSS数值:{MLSS}mg/L, 水面图片视觉描述和分析的内容:{filter_result},代表你可以看到水面相关情况。为了防止你对数值判断不准确，为你提供数值比较后的内容:{parameters_evaluation}。请根据输入内容和提示词进行回答。",
        }
    ]
    
    for retry in range(MAX_RETRIES):
        try:
            resp = client.chat.completions.create(
                model=model,
                messages=messages,
                seed=42
            )
            response_text = resp.choices[0].message.content
            logging.info(f"已经获得响应")
            logging.info(f"获得响应: {response_text}")
            return response_text
        except Exception as e:
            logging.error(f"请求失败, 尝试重新请求 ({retry+1}/{MAX_RETRIES}): {e}")

    logging.error("请求失败次数过多,已达到最大重试次数,请检查网络或API密钥")
    return None
# 定义一个用于处理和分析排泥时间的模型
def llm_process_sludge_discharge(sludge_discharge_result: str):

    # 使用配置实例中的环境变量
    client = OpenAI(
        api_key=config.env_vars.get("GLM_API_KEY"),
        base_url=config.env_vars.get("GLM_BASE_URL")
    )
    model = config.env_vars.get("GLM_MODEL")
    
    # query = "根据提示词分析并输出内容。"

    system_prompt = SYSTEM_SLUDGE_DISCHARGE['system_prompt_sludge_discharge']


    messages = [
        {'role': 'system', 'content': system_prompt},
        {
        'role': 'user',
            'content': f"计算出的相关内容:{sludge_discharge_result}以下是好氧池的设计参数：1）内回流比：50-150%;2）外回流比：50-100% ;3）处理水量：22000m³/d;",
        }
    ]
    
    for retry in range(MAX_RETRIES):
        try:
            resp = client.chat.completions.create(
                model=model,
                messages=messages,
                seed=42
            )
            response_text = resp.choices[0].message.content
            logging.info(f"获得响应: {response_text}")
            return response_text
        except Exception as e:
            logging.error(f"请求失败, 尝试重新请求 ({retry+1}/{MAX_RETRIES}): {e}")

    logging.error("请求失败次数过多,已达到最大重试次数,请检查网络或API密钥")
    return None

if __name__ == "__main__":
    # run_server()

    # print(llm_process_image_filter(filter_result='图片中水面存在明显的泡沫，且泡沫分布不均匀，表明反冲洗过程不均匀。左侧的泡沫较少，右侧的泡沫较多，这可能是由于反冲洗水流分布不均或滤层膨胀不充分导致的。', system_type='system_prompt_filter1'))
    # print(llm_process_image_filter(filter_result='水面平静，没有明显的泡沫出现，表明不是反冲洗阶段。水面均匀，没有异常的积泥、气泡或污染物聚集，水流状态正常。', system_type='system_prompt_filter1'))
    print(llm_process_image_aerobic(filter_result='图片中可以看到好氧池的水面存在大量的泡沫和泡沫，泡沫的覆盖率约为20%。泡沫的颜色为灰白色，泡沫较为密集，分布在水面的大部分区域。这种情况可能是由于曝气不足或污泥膨胀导致的。', DO=2.5, MLSS=2210, bubble_area='20%', system_type='system_prompt_aerobic_single2'))