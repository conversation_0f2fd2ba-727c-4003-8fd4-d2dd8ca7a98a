# 视觉大模型下载的文件
#  model_name = "OpenGVLab/InternVL2-8B-AWQ" 或者下载 model_name = "qwen2-vl-7b-instruct-awq"

import os
os.environ["HF_ENDPOINT"] = "https://hf-mirror.com"  # 设置为hf的国内镜像网站

from huggingface_hub import snapshot_download

model_name = "Qwen/Qwen2-VL-7B-Instruct-AWQ"
local_dir = os.path.join("llms/models/visionmodel/data")

# while True 是为了防止断联
while True:
    try:
        snapshot_download(
            repo_id=model_name,
            local_dir_use_symlinks=True,  # 在local-dir指定的目录中都是一些“链接文件”
           # ignore_patterns=["*.bin"],  # 忽略下载哪些文件
            local_dir=local_dir, # 模型下载到本地的目录
            token="*************",   # hug/gingface的token
            resume_download=True
        )
        break
    except:
        pass